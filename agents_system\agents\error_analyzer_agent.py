"""
Agent d'analyse d'erreurs utilisant ChatGPT 4.1
"""

import time
import os
import sys
from typing import Dict, Any, List, Optional

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.base_agent import BaseAgent, AgentResult
from tools.chatgpt_client import ChatGPTClient
from tools.code_analyzer import CodeAnalyzer
from tools.error_analyzer import <PERSON>rror<PERSON>nalyzer
from utils.file_handler import FileHandler
from utils.validators import FileValidator, ContentValidator
from config.config import DEFAULT_CONFIG

class ErrorAnalyzerAgent(BaseAgent):
    """Agent spécialisé dans l'analyse d'erreurs de code"""
    
    def __init__(self):
        """Initialise l'agent d'analyse d'erreurs"""
        config = DEFAULT_CONFIG.get_agent_config('error_analyzer')
        super().__init__('error_analyzer', config)
        
        # Initialiser les outils
        self.chatgpt_client = ChatGPTClient(
            api_key=DEFAULT_CONFIG.OPENAI_API_KEY,
            model=DEFAULT_CONFIG.OPENAI_MODEL
        )
        self.code_analyzer = CodeAnalyzer()
        self.error_analyzer = ErrorAnalyzer()
        self.file_handler = FileHandler()
        
        self.log_info("Agent d'analyse d'erreurs initialisé avec ChatGPT 4.1")
    
    def validate_inputs(self, program_file: str, error_file: str) -> bool:
        """
        Valide les fichiers d'entrée
        
        Args:
            program_file: Chemin vers le fichier programme
            error_file: Chemin vers le fichier d'erreur
        
        Returns:
            True si les fichiers sont valides
        """
        # Vérifier l'existence des fichiers
        if not FileValidator.validate_file_exists(program_file):
            self.log_error(f"Fichier programme non trouvé: {program_file}")
            return False
        
        if not FileValidator.validate_file_exists(error_file):
            self.log_error(f"Fichier d'erreur non trouvé: {error_file}")
            return False
        
        # Vérifier la lisibilité
        if not FileValidator.validate_file_readable(program_file):
            self.log_error(f"Fichier programme non lisible: {program_file}")
            return False
        
        if not FileValidator.validate_file_readable(error_file):
            self.log_error(f"Fichier d'erreur non lisible: {error_file}")
            return False
        
        # Vérifier les extensions
        if not FileValidator.validate_file_extension(
            program_file, 
            DEFAULT_CONFIG.SUPPORTED_PROGRAM_EXTENSIONS
        ):
            self.log_warning(f"Extension de fichier programme non standard: {program_file}")
        
        if not FileValidator.validate_file_extension(
            error_file, 
            DEFAULT_CONFIG.SUPPORTED_ERROR_EXTENSIONS
        ):
            self.log_warning(f"Extension de fichier d'erreur non standard: {error_file}")
        
        return True
    
    def analyze_files(self, program_file: str, error_file: str) -> str:
        """
        Analyse les fichiers de programme et d'erreur
        
        Args:
            program_file: Chemin vers le fichier programme
            error_file: Chemin vers le fichier d'erreur
        
        Returns:
            Résultat de l'analyse
        """
        start_time = time.time()
        
        try:
            # Validation des entrées
            if not self.validate_inputs(program_file, error_file):
                return "Erreur: Fichiers d'entrée invalides"
            
            self.log_info(f"Début de l'analyse des fichiers:")
            self.log_info(f"  Programme: {program_file}")
            self.log_info(f"  Erreurs: {error_file}")
            
            # Lire les fichiers
            program_content = self.file_handler.read_file(program_file)
            error_content = self.file_handler.read_file(error_file)
            
            self.log_info(f"Fichiers lus avec succès")
            self.log_info(f"  Programme: {len(program_content)} caractères")
            self.log_info(f"  Erreurs: {len(error_content)} caractères")
            
            # Valider le contenu
            if not ContentValidator.is_program_file(program_content):
                self.log_warning("Le fichier programme ne semble pas contenir de code")
            
            if not ContentValidator.is_error_file(error_content):
                self.log_warning("Le fichier d'erreur ne semble pas contenir d'erreurs")
            
            # Analyse locale avec les outils
            local_analysis = self._perform_local_analysis(program_content, error_content)
            
            # Analyse avec ChatGPT 4.1
            chatgpt_analysis = self._perform_chatgpt_analysis(program_content, error_content)
            
            # Combiner les résultats
            combined_result = self._combine_analyses(local_analysis, chatgpt_analysis)
            
            execution_time = time.time() - start_time
            self.log_info(f"Analyse terminée en {execution_time:.2f} secondes")
            
            return combined_result
            
        except Exception as e:
            self.log_error(f"Erreur lors de l'analyse: {str(e)}")
            return f"Erreur lors de l'analyse: {str(e)}"

    def _perform_local_analysis(self, program_content: str, error_content: str) -> Dict[str, Any]:
        """
        Effectue une analyse locale avec les outils intégrés

        Args:
            program_content: Contenu du fichier programme
            error_content: Contenu du fichier d'erreur

        Returns:
            Résultats de l'analyse locale
        """
        self.log_info("Début de l'analyse locale")

        # Analyser le code
        code_errors = self.code_analyzer.analyze_code(program_content, "c")
        code_structure = self.code_analyzer.analyze_structure(program_content)

        # Analyser les erreurs
        error_entries = self.error_analyzer.analyze_error_file(error_content)
        error_summary = self.error_analyzer.get_error_summary(error_entries)
        error_categories = self.error_analyzer.categorize_errors(error_entries)

        self.log_info(f"Analyse locale terminée:")
        self.log_info(f"  Erreurs de code détectées: {len(code_errors)}")
        self.log_info(f"  Entrées d'erreur trouvées: {len(error_entries)}")

        return {
            'code_errors': code_errors,
            'code_structure': code_structure,
            'error_entries': error_entries,
            'error_summary': error_summary,
            'error_categories': error_categories
        }

    def _perform_chatgpt_analysis(self, program_content: str, error_content: str) -> str:
        """
        Effectue une analyse avec ChatGPT 4.1

        Args:
            program_content: Contenu du fichier programme
            error_content: Contenu du fichier d'erreur

        Returns:
            Analyse de ChatGPT
        """
        self.log_info("Début de l'analyse avec ChatGPT 4.1")

        try:
            # Valider la clé API
            if not self.chatgpt_client.validate_api_key():
                self.log_error("Clé API ChatGPT invalide")
                return "Erreur: Clé API ChatGPT invalide"

            # Effectuer l'analyse
            response = self.chatgpt_client.analyze_code_errors(
                program_content=program_content,
                error_content=error_content,
                agent_prompt=self.prompt
            )

            self.log_info(f"Analyse ChatGPT terminée:")
            self.log_info(f"  Tokens utilisés: {response.usage}")
            self.log_info(f"  Temps de réponse: {response.response_time:.2f}s")

            return response.content

        except Exception as e:
            self.log_error(f"Erreur lors de l'analyse ChatGPT: {str(e)}")
            return f"Erreur lors de l'analyse ChatGPT: {str(e)}"

    def _combine_analyses(self, local_analysis: Dict[str, Any], chatgpt_analysis: str) -> str:
        """
        Combine les résultats des analyses locale et ChatGPT en un rapport structuré et détaillé

        Args:
            local_analysis: Résultats de l'analyse locale
            chatgpt_analysis: Résultats de l'analyse ChatGPT

        Returns:
            Rapport combiné formaté avec structure améliorée
        """
        self.log_info("Combinaison des analyses avec format amélioré")

        result = []

        # En-tête principal avec informations de contexte
        result.append("═" * 100)
        result.append("🔍 RAPPORT D'ANALYSE APPROFONDIE - SYSTÈME CAOFORS")
        result.append("═" * 100)
        result.append(f"📅 Date d'analyse : {time.strftime('%Y-%m-%d %H:%M:%S')}")
        result.append(f"🤖 Moteur d'analyse : Agent ErrorAnalyzer v2.0 + ChatGPT 4.1")
        result.append(f"🔧 Mode d'analyse : Hybride (Locale + IA)")
        result.append("")

        # Dashboard exécutif avec métriques clés
        error_summary = local_analysis['error_summary']
        code_structure = local_analysis['code_structure']

        result.append("📊 DASHBOARD EXÉCUTIF")
        result.append("═" * 50)

        # Calcul du score de santé du code
        total_lines = code_structure['total_lines']
        total_errors = error_summary['total_errors']

        # Calcul des erreurs critiques depuis by_severity si disponible
        critical_errors = 0
        if 'by_severity' in error_summary and error_summary['by_severity']:
            critical_errors = error_summary['by_severity'].get('CRITICAL', 0)

        if total_lines > 0:
            error_density = (total_errors / total_lines) * 100
            if critical_errors > 0:
                health_score = max(0, 100 - (critical_errors * 20) - (total_errors * 5))
            else:
                health_score = max(0, 100 - (total_errors * 3))
        else:
            error_density = 0
            health_score = 50

        # Détermination du niveau de criticité
        if critical_errors > 0:
            criticality_level = "🔴 CRITIQUE"
            criticality_emoji = "🚨"
        elif total_errors > 10:
            criticality_level = "🟠 ÉLEVÉ"
            criticality_emoji = "⚠️"
        elif total_errors > 5:
            criticality_level = "🟡 MODÉRÉ"
            criticality_emoji = "⚡"
        else:
            criticality_level = "🟢 FAIBLE"
            criticality_emoji = "✅"

        result.append(f"🎯 Score de santé du code : {health_score:.1f}/100")
        result.append(f"📈 Densité d'erreurs : {error_density:.2f}% ({total_errors} erreurs sur {total_lines} lignes)")
        result.append(f"🚨 Niveau de criticité : {criticality_level}")
        result.append(f"⚡ Erreurs critiques : {critical_errors}")
        result.append(f"⚠️ Total erreurs détectées : {total_errors}")

        # Avertissements si disponibles
        if 'warnings' in error_summary:
            result.append(f"📝 Avertissements : {error_summary['warnings']}")
        result.append("")

        # Répartition détaillée par sévérité avec pourcentages
        if error_summary.get('by_severity'):
            result.append("📊 RÉPARTITION PAR SÉVÉRITÉ")
            result.append("─" * 35)
            for severity, count in error_summary['by_severity'].items():
                percentage = (count / total_errors * 100) if total_errors > 0 else 0
                severity_emoji = "🔴" if severity == "CRITICAL" else "🟠" if severity == "HIGH" else "🟡" if severity == "MEDIUM" else "🟢"
                result.append(f"  {severity_emoji} {severity}: {count} ({percentage:.1f}%)")

        # Répartition détaillée par type avec pourcentages
        if error_summary.get('by_type'):
            result.append("")
            result.append("📊 RÉPARTITION PAR TYPE D'ERREUR")
            result.append("─" * 40)
            for error_type, count in error_summary['by_type'].items():
                percentage = (count / total_errors * 100) if total_errors > 0 else 0
                result.append(f"  {criticality_emoji} {error_type}: {count} ({percentage:.1f}%)")
        result.append("")

        # Analyse détaillée des erreurs critiques
        error_entries = local_analysis['error_entries']
        if error_entries:
            critical_entries = [e for e in error_entries if hasattr(e, 'severity') and e.severity == "CRITICAL"]
            high_entries = [e for e in error_entries if hasattr(e, 'severity') and e.severity == "HIGH"]

            if critical_entries:
                result.append("🚨 ERREURS CRITIQUES - ATTENTION IMMÉDIATE REQUISE")
                result.append("═" * 60)
                for i, error in enumerate(critical_entries, 1):
                    result.append(f"🔴 ERREUR CRITIQUE #{i}")
                    result.append(f"   📍 Localisation : Ligne {error.line_number}")
                    result.append(f"   🎯 Type : {error.error_type.value}")
                    result.append(f"   💥 Message : {error.message}")
                    result.append(f"   📝 Détails : {error.raw_line}")
                    if hasattr(error, 'timestamp') and error.timestamp:
                        result.append(f"   🕒 Horodatage : {error.timestamp}")
                    result.append(f"   ⚡ Impact : Peut bloquer l'exécution du système")
                    result.append("")

            if high_entries:
                result.append("⚠️ ERREURS IMPORTANTES - RÉSOLUTION PRIORITAIRE")
                result.append("═" * 55)
                for i, error in enumerate(high_entries, 1):
                    result.append(f"🟠 ERREUR IMPORTANTE #{i}")
                    result.append(f"   📍 Localisation : Ligne {error.line_number}")
                    result.append(f"   🎯 Type : {error.error_type.value}")
                    result.append(f"   💥 Message : {error.message}")
                    result.append(f"   📝 Détails : {error.raw_line}")
                    if hasattr(error, 'timestamp') and error.timestamp:
                        result.append(f"   🕒 Horodatage : {error.timestamp}")
                    result.append("")

            # Toutes les autres erreurs
            other_entries = [e for e in error_entries if not hasattr(e, 'severity') or e.severity not in ["CRITICAL", "HIGH"]]
            if other_entries:
                result.append("📋 AUTRES ERREURS DÉTECTÉES")
                result.append("═" * 35)
                for i, error in enumerate(other_entries, 1):
                    severity_display = getattr(error, 'severity', 'UNKNOWN')
                    result.append(f"🟡 ERREUR #{i} - Sévérité: {severity_display}")
                    result.append(f"   📍 Localisation : Ligne {error.line_number}")
                    result.append(f"   🎯 Type : {error.error_type.value}")
                    result.append(f"   💥 Message : {error.message}")
                    result.append(f"   📝 Détails : {error.raw_line}")
                    if hasattr(error, 'timestamp') and error.timestamp:
                        result.append(f"   🕒 Horodatage : {error.timestamp}")
                    result.append("")

        # Analyse approfondie du code source
        code_errors = local_analysis['code_errors']
        if code_errors:
            result.append("🔍 ANALYSE APPROFONDIE DU CODE SOURCE")
            result.append("═" * 50)

            # Grouper par type d'erreur
            errors_by_type = {}
            for error in code_errors:
                error_type = error.error_type
                if error_type not in errors_by_type:
                    errors_by_type[error_type] = []
                errors_by_type[error_type].append(error)

            for error_type, errors in errors_by_type.items():
                result.append(f"📂 CATÉGORIE : {error_type.upper()}")
                result.append("─" * 40)
                for i, error in enumerate(errors, 1):
                    severity_value = error.severity.value if hasattr(error.severity, 'value') else str(error.severity)
                    severity_emoji = "🔴" if severity_value == "CRITICAL" else "🟠" if severity_value == "HIGH" else "🟡"
                    result.append(f"  {severity_emoji} Problème #{i}")
                    result.append(f"     📍 Ligne {error.line_number} : {error.code_snippet}")
                    result.append(f"     💬 Description : {error.message}")
                    result.append(f"     🛠️ Suggestion : {error.suggestion}")
                    result.append(f"     ⚡ Sévérité : {severity_value}")
                    result.append("")

        # Métriques détaillées de la structure du code
        result.append("📋 ANALYSE STRUCTURELLE DÉTAILLÉE")
        result.append("═" * 45)

        # Calcul de métriques avancées
        code_lines = code_structure['code_lines']
        comment_lines = code_structure['comment_lines']
        blank_lines = code_structure['blank_lines']

        if code_lines > 0:
            comment_ratio = (comment_lines / code_lines) * 100
            complexity_indicator = "🟢 SIMPLE" if code_lines < 500 else "🟡 MODÉRÉE" if code_lines < 1500 else "🟠 COMPLEXE" if code_lines < 3000 else "🔴 TRÈS COMPLEXE"
        else:
            comment_ratio = 0
            complexity_indicator = "❓ INDÉTERMINÉE"

        result.append(f"📊 Métriques générales :")
        result.append(f"   📝 Lignes totales : {total_lines:,}")
        result.append(f"   💻 Lignes de code : {code_lines:,}")
        result.append(f"   💬 Lignes de commentaires : {comment_lines:,} ({comment_ratio:.1f}%)")
        result.append(f"   ⬜ Lignes vides : {blank_lines:,}")
        result.append(f"   🔧 Complexité estimée : {complexity_indicator}")
        result.append("")

        result.append(f"🏗️ Architecture du code :")
        result.append(f"   🔧 Fonctions détectées : {len(code_structure['functions'])}")
        result.append(f"   📦 Includes/Imports : {len(code_structure['includes'])}")

        # Affichage des fonctions principales si disponibles
        if code_structure['functions']:
            result.append(f"   📋 Fonctions principales :")
            for func in code_structure['functions'][:10]:  # Limiter à 10 fonctions
                result.append(f"      • {func}")
            if len(code_structure['functions']) > 10:
                result.append(f"      ... et {len(code_structure['functions']) - 10} autres")
        result.append("")

        # Analyse ChatGPT avec mise en forme améliorée
        result.append("🤖 ANALYSE EXPERTE PAR INTELLIGENCE ARTIFICIELLE")
        result.append("═" * 60)
        result.append("🧠 Analyse réalisée par ChatGPT 4.1 - Modèle expert en analyse de code")
        result.append("─" * 60)
        result.append(chatgpt_analysis)
        result.append("")

        # Pied de page avec recommandations générales
        result.append("═" * 100)
        result.append("📋 RECOMMANDATIONS GÉNÉRALES DE SUIVI")
        result.append("═" * 100)
        result.append("🔄 Actions recommandées :")
        result.append("   1. 🚨 Traiter en priorité les erreurs critiques identifiées")
        result.append("   2. 🧪 Effectuer des tests approfondis après corrections")
        result.append("   3. 📚 Documenter les solutions appliquées")
        result.append("   4. 🔍 Planifier une révision de code complète")
        result.append("   5. 📊 Mettre en place un monitoring continu")
        result.append("")
        result.append("⏰ Prochaine analyse recommandée : Dans 24-48h après corrections")
        result.append("═" * 100)

        return "\n".join(result)

    def execute(self, program_file: str, error_file: str) -> AgentResult:
        """
        Exécute l'analyse d'erreurs

        Args:
            program_file: Chemin vers le fichier programme
            error_file: Chemin vers le fichier d'erreur

        Returns:
            Résultat de l'exécution
        """
        start_time = time.time()

        try:
            result = self.analyze_files(program_file, error_file)
            execution_time = time.time() - start_time

            return AgentResult(
                success=True,
                data=result,
                message="Analyse d'erreurs terminée avec succès",
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return AgentResult(
                success=False,
                data=None,
                message=f"Erreur lors de l'analyse: {str(e)}",
                execution_time=execution_time,
                errors=[str(e)]
            )
