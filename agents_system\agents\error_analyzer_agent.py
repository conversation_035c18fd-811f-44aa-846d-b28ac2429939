"""
Agent d'analyse d'erreurs utilisant ChatGPT 4.1
"""

import time
import os
import sys
from typing import Dict, Any, List, Optional

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.base_agent import BaseAgent, AgentResult
from tools.chatgpt_client import ChatGPTClient
from tools.code_analyzer import CodeAnalyzer
from tools.error_analyzer import <PERSON>rror<PERSON>nalyzer
from utils.file_handler import FileHandler
from utils.validators import FileValidator, ContentValidator
from config.config import DEFAULT_CONFIG

class ErrorAnalyzerAgent(BaseAgent):
    """Agent spécialisé dans l'analyse d'erreurs de code"""
    
    def __init__(self):
        """Initialise l'agent d'analyse d'erreurs"""
        config = DEFAULT_CONFIG.get_agent_config('error_analyzer')
        super().__init__('error_analyzer', config)
        
        # Initialiser les outils
        self.chatgpt_client = ChatGPTClient(
            api_key=DEFAULT_CONFIG.OPENAI_API_KEY,
            model=DEFAULT_CONFIG.OPENAI_MODEL
        )
        self.code_analyzer = CodeAnalyzer()
        self.error_analyzer = ErrorAnalyzer()
        self.file_handler = FileHandler()
        
        self.log_info("Agent d'analyse d'erreurs initialisé avec ChatGPT 4.1")
    
    def validate_inputs(self, program_file: str, error_file: str) -> bool:
        """
        Valide les fichiers d'entrée
        
        Args:
            program_file: Chemin vers le fichier programme
            error_file: Chemin vers le fichier d'erreur
        
        Returns:
            True si les fichiers sont valides
        """
        # Vérifier l'existence des fichiers
        if not FileValidator.validate_file_exists(program_file):
            self.log_error(f"Fichier programme non trouvé: {program_file}")
            return False
        
        if not FileValidator.validate_file_exists(error_file):
            self.log_error(f"Fichier d'erreur non trouvé: {error_file}")
            return False
        
        # Vérifier la lisibilité
        if not FileValidator.validate_file_readable(program_file):
            self.log_error(f"Fichier programme non lisible: {program_file}")
            return False
        
        if not FileValidator.validate_file_readable(error_file):
            self.log_error(f"Fichier d'erreur non lisible: {error_file}")
            return False
        
        # Vérifier les extensions
        if not FileValidator.validate_file_extension(
            program_file, 
            DEFAULT_CONFIG.SUPPORTED_PROGRAM_EXTENSIONS
        ):
            self.log_warning(f"Extension de fichier programme non standard: {program_file}")
        
        if not FileValidator.validate_file_extension(
            error_file, 
            DEFAULT_CONFIG.SUPPORTED_ERROR_EXTENSIONS
        ):
            self.log_warning(f"Extension de fichier d'erreur non standard: {error_file}")
        
        return True
    
    def analyze_files(self, program_file: str, error_file: str) -> str:
        """
        Analyse les fichiers de programme et d'erreur
        
        Args:
            program_file: Chemin vers le fichier programme
            error_file: Chemin vers le fichier d'erreur
        
        Returns:
            Résultat de l'analyse
        """
        start_time = time.time()
        
        try:
            # Validation des entrées
            if not self.validate_inputs(program_file, error_file):
                return "Erreur: Fichiers d'entrée invalides"
            
            self.log_info(f"Début de l'analyse des fichiers:")
            self.log_info(f"  Programme: {program_file}")
            self.log_info(f"  Erreurs: {error_file}")
            
            # Lire les fichiers
            program_content = self.file_handler.read_file(program_file)
            error_content = self.file_handler.read_file(error_file)
            
            self.log_info(f"Fichiers lus avec succès")
            self.log_info(f"  Programme: {len(program_content)} caractères")
            self.log_info(f"  Erreurs: {len(error_content)} caractères")
            
            # Valider le contenu
            if not ContentValidator.is_program_file(program_content):
                self.log_warning("Le fichier programme ne semble pas contenir de code")
            
            if not ContentValidator.is_error_file(error_content):
                self.log_warning("Le fichier d'erreur ne semble pas contenir d'erreurs")
            
            # Analyse locale avec les outils
            local_analysis = self._perform_local_analysis(program_content, error_content)
            
            # Analyse avec ChatGPT 4.1
            chatgpt_analysis = self._perform_chatgpt_analysis(program_content, error_content)
            
            # Combiner les résultats
            combined_result = self._combine_analyses(local_analysis, chatgpt_analysis)
            
            execution_time = time.time() - start_time
            self.log_info(f"Analyse terminée en {execution_time:.2f} secondes")
            
            return combined_result
            
        except Exception as e:
            self.log_error(f"Erreur lors de l'analyse: {str(e)}")
            return f"Erreur lors de l'analyse: {str(e)}"

    def _perform_local_analysis(self, program_content: str, error_content: str) -> Dict[str, Any]:
        """
        Effectue une analyse locale avec les outils intégrés

        Args:
            program_content: Contenu du fichier programme
            error_content: Contenu du fichier d'erreur

        Returns:
            Résultats de l'analyse locale
        """
        self.log_info("Début de l'analyse locale")

        # Analyser le code
        code_errors = self.code_analyzer.analyze_code(program_content, "c")
        code_structure = self.code_analyzer.analyze_structure(program_content)

        # Analyser les erreurs
        error_entries = self.error_analyzer.analyze_error_file(error_content)
        error_summary = self.error_analyzer.get_error_summary(error_entries)
        error_categories = self.error_analyzer.categorize_errors(error_entries)

        self.log_info(f"Analyse locale terminée:")
        self.log_info(f"  Erreurs de code détectées: {len(code_errors)}")
        self.log_info(f"  Entrées d'erreur trouvées: {len(error_entries)}")

        return {
            'code_errors': code_errors,
            'code_structure': code_structure,
            'error_entries': error_entries,
            'error_summary': error_summary,
            'error_categories': error_categories
        }

    def _perform_chatgpt_analysis(self, program_content: str, error_content: str) -> str:
        """
        Effectue une analyse avec ChatGPT 4.1

        Args:
            program_content: Contenu du fichier programme
            error_content: Contenu du fichier d'erreur

        Returns:
            Analyse de ChatGPT
        """
        self.log_info("Début de l'analyse avec ChatGPT 4.1")

        try:
            # Valider la clé API
            if not self.chatgpt_client.validate_api_key():
                self.log_error("Clé API ChatGPT invalide")
                return "Erreur: Clé API ChatGPT invalide"

            # Effectuer l'analyse
            response = self.chatgpt_client.analyze_code_errors(
                program_content=program_content,
                error_content=error_content,
                agent_prompt=self.prompt
            )

            self.log_info(f"Analyse ChatGPT terminée:")
            self.log_info(f"  Tokens utilisés: {response.usage}")
            self.log_info(f"  Temps de réponse: {response.response_time:.2f}s")

            return response.content

        except Exception as e:
            self.log_error(f"Erreur lors de l'analyse ChatGPT: {str(e)}")
            return f"Erreur lors de l'analyse ChatGPT: {str(e)}"

    def _combine_analyses(self, local_analysis: Dict[str, Any], chatgpt_analysis: str) -> str:
        """
        Combine les résultats des analyses locale et ChatGPT

        Args:
            local_analysis: Résultats de l'analyse locale
            chatgpt_analysis: Résultats de l'analyse ChatGPT

        Returns:
            Analyse combinée formatée
        """
        self.log_info("Combinaison des analyses")

        result = []

        # En-tête
        result.append("RAPPORT D'ANALYSE D'ERREURS")
        result.append("=" * 50)
        result.append("")

        # Résumé des erreurs du fichier d'erreur
        error_summary = local_analysis['error_summary']
        result.append("📊 RÉSUMÉ DES ERREURS")
        result.append("-" * 25)
        result.append(f"Total des erreurs détectées: {error_summary['total_errors']}")

        if error_summary['by_severity']:
            result.append("Répartition par sévérité:")
            for severity, count in error_summary['by_severity'].items():
                result.append(f"  • {severity}: {count}")

        if error_summary['by_type']:
            result.append("Répartition par type:")
            for error_type, count in error_summary['by_type'].items():
                result.append(f"  • {error_type}: {count}")

        result.append("")

        # Erreurs détectées dans le fichier d'erreur
        error_entries = local_analysis['error_entries']
        if error_entries:
            result.append("🚨 ERREURS DÉTECTÉES DANS LE FICHIER D'ERREUR")
            result.append("-" * 45)
            for i, error in enumerate(error_entries, 1):
                result.append(f"{i}. {error.message}")
                result.append(f"   Type: {error.error_type.value}")
                result.append(f"   Sévérité: {error.severity}")
                result.append(f"   Ligne {error.line_number}: {error.raw_line}")
                if error.timestamp:
                    result.append(f"   Timestamp: {error.timestamp}")
                result.append("")

        # Problèmes détectés dans le code
        code_errors = local_analysis['code_errors']
        if code_errors:
            result.append("🔍 PROBLÈMES DÉTECTÉS DANS LE CODE")
            result.append("-" * 35)
            for i, error in enumerate(code_errors, 1):
                result.append(f"{i}. {error.message}")
                result.append(f"   Type: {error.error_type}")
                result.append(f"   Sévérité: {error.severity.value}")
                result.append(f"   Ligne {error.line_number}: {error.code_snippet}")
                result.append(f"   Suggestion: {error.suggestion}")
                result.append("")

        # Structure du code
        code_structure = local_analysis['code_structure']
        result.append("📋 STRUCTURE DU CODE")
        result.append("-" * 20)
        result.append(f"Lignes totales: {code_structure['total_lines']}")
        result.append(f"Lignes de code: {code_structure['code_lines']}")
        result.append(f"Lignes de commentaires: {code_structure['comment_lines']}")
        result.append(f"Lignes vides: {code_structure['blank_lines']}")
        result.append(f"Fonctions détectées: {len(code_structure['functions'])}")
        result.append(f"Includes détectés: {len(code_structure['includes'])}")
        result.append("")

        # Analyse ChatGPT
        result.append("🤖 ANALYSE CHATGPT 4.1")
        result.append("-" * 25)
        result.append(chatgpt_analysis)
        result.append("")

        return "\n".join(result)

    def execute(self, program_file: str, error_file: str) -> AgentResult:
        """
        Exécute l'analyse d'erreurs

        Args:
            program_file: Chemin vers le fichier programme
            error_file: Chemin vers le fichier d'erreur

        Returns:
            Résultat de l'exécution
        """
        start_time = time.time()

        try:
            result = self.analyze_files(program_file, error_file)
            execution_time = time.time() - start_time

            return AgentResult(
                success=True,
                data=result,
                message="Analyse d'erreurs terminée avec succès",
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return AgentResult(
                success=False,
                data=None,
                message=f"Erreur lors de l'analyse: {str(e)}",
                execution_time=execution_time,
                errors=[str(e)]
            )
