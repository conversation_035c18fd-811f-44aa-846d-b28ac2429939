"""
Outil d'analyse des fichiers d'erreur
"""

import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

class ErrorType(Enum):
    """Types d'erreurs"""
    SYSTEM_ERROR = "SYSTEM_ERROR"
    APPLICATION_ERROR = "APPLICATION_ERROR"
    DATABASE_ERROR = "DATABASE_ERROR"
    NETWORK_ERROR = "NETWORK_ERROR"
    PERMISSION_ERROR = "PERMISSION_ERROR"
    RESOURCE_ERROR = "RESOURCE_ERROR"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"
    UNKNOWN = "UNKNOWN"

@dataclass
class ErrorEntry:
    """Représente une entrée d'erreur"""
    timestamp: Optional[datetime]
    error_type: ErrorType
    severity: str
    message: str
    details: str
    line_number: int
    raw_line: str

class ErrorAnalyzer:
    """Analyseur de fichiers d'erreur"""
    
    def __init__(self):
        self.error_patterns = self._init_error_patterns()
        self.timestamp_patterns = self._init_timestamp_patterns()
    
    def _init_error_patterns(self) -> List[Dict[str, Any]]:
        """Initialise les patterns de détection d'erreurs"""
        return [
            {
                'pattern': r'locked\s*\(already\s+runs\s*\)',
                'type': ErrorType.RESOURCE_ERROR,
                'severity': 'HIGH',
                'description': 'Processus déjà en cours d\'exécution'
            },
            {
                'pattern': r'ERROR\s+task\s+\[([^\]]+)\]',
                'type': ErrorType.APPLICATION_ERROR,
                'severity': 'HIGH',
                'description': 'Erreur de tâche application'
            },
            {
                'pattern': r'SQLCODE\s*=\s*(-?\d+)',
                'type': ErrorType.DATABASE_ERROR,
                'severity': 'HIGH',
                'description': 'Erreur base de données'
            },
            {
                'pattern': r'permission\s+denied|access\s+denied',
                'type': ErrorType.PERMISSION_ERROR,
                'severity': 'HIGH',
                'description': 'Erreur de permissions'
            },
            {
                'pattern': r'connection\s+refused|network\s+error',
                'type': ErrorType.NETWORK_ERROR,
                'severity': 'HIGH',
                'description': 'Erreur réseau'
            },
            {
                'pattern': r'file\s+not\s+found|no\s+such\s+file',
                'type': ErrorType.SYSTEM_ERROR,
                'severity': 'MEDIUM',
                'description': 'Fichier non trouvé'
            },
            {
                'pattern': r'out\s+of\s+memory|memory\s+allocation',
                'type': ErrorType.RESOURCE_ERROR,
                'severity': 'CRITICAL',
                'description': 'Problème de mémoire'
            },
            {
                'pattern': r'configuration\s+error|config\s+invalid',
                'type': ErrorType.CONFIGURATION_ERROR,
                'severity': 'MEDIUM',
                'description': 'Erreur de configuration'
            },
            {
                'pattern': r'EXIT|ABORT|FATAL',
                'type': ErrorType.APPLICATION_ERROR,
                'severity': 'CRITICAL',
                'description': 'Arrêt critique de l\'application'
            }
        ]
    
    def _init_timestamp_patterns(self) -> List[str]:
        """Initialise les patterns de timestamp"""
        return [
            r'(\d{2}\.\d{2}\.\d{4}\s+\d{2}:\d{2}:\d{2})',  # DD.MM.YYYY HH:MM:SS
            r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',   # YYYY-MM-DD HH:MM:SS
            r'(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})',   # MM/DD/YYYY HH:MM:SS
        ]
    
    def parse_timestamp(self, text: str) -> Optional[datetime]:
        """
        Parse un timestamp depuis le texte
        
        Args:
            text: Texte contenant le timestamp
        
        Returns:
            Objet datetime ou None
        """
        for pattern in self.timestamp_patterns:
            match = re.search(pattern, text)
            if match:
                timestamp_str = match.group(1)
                
                # Essayer différents formats
                formats = [
                    '%d.%m.%Y %H:%M:%S',
                    '%Y-%m-%d %H:%M:%S',
                    '%m/%d/%Y %H:%M:%S'
                ]
                
                for fmt in formats:
                    try:
                        return datetime.strptime(timestamp_str, fmt)
                    except ValueError:
                        continue
        
        return None
    
    def analyze_error_file(self, content: str) -> List[ErrorEntry]:
        """
        Analyse un fichier d'erreur et extrait les erreurs
        
        Args:
            content: Contenu du fichier d'erreur
        
        Returns:
            Liste des erreurs trouvées
        """
        errors = []
        lines = content.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            if not line.strip():
                continue
            
            # Extraire le timestamp
            timestamp = self.parse_timestamp(line)
            
            # Analyser les patterns d'erreur
            for pattern_info in self.error_patterns:
                match = re.search(pattern_info['pattern'], line, re.IGNORECASE)
                if match:
                    error = ErrorEntry(
                        timestamp=timestamp,
                        error_type=pattern_info['type'],
                        severity=pattern_info['severity'],
                        message=pattern_info['description'],
                        details=match.group(0) if match.groups() else line.strip(),
                        line_number=line_num,
                        raw_line=line.strip()
                    )
                    errors.append(error)
                    break  # Une seule erreur par ligne
        
        return errors
    
    def categorize_errors(self, errors: List[ErrorEntry]) -> Dict[str, List[ErrorEntry]]:
        """
        Catégorise les erreurs par type
        
        Args:
            errors: Liste des erreurs
        
        Returns:
            Dictionnaire des erreurs par catégorie
        """
        categories = {}
        
        for error in errors:
            category = error.error_type.value
            if category not in categories:
                categories[category] = []
            categories[category].append(error)
        
        return categories
    
    def get_error_summary(self, errors: List[ErrorEntry]) -> Dict[str, Any]:
        """
        Génère un résumé des erreurs
        
        Args:
            errors: Liste des erreurs
        
        Returns:
            Résumé des erreurs
        """
        if not errors:
            return {
                'total_errors': 0,
                'by_severity': {},
                'by_type': {},
                'timeline': []
            }
        
        # Compter par sévérité
        by_severity = {}
        for error in errors:
            severity = error.severity
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        # Compter par type
        by_type = {}
        for error in errors:
            error_type = error.error_type.value
            by_type[error_type] = by_type.get(error_type, 0) + 1
        
        # Timeline des erreurs
        timeline = []
        for error in errors:
            if error.timestamp:
                timeline.append({
                    'timestamp': error.timestamp.isoformat(),
                    'message': error.message,
                    'severity': error.severity
                })
        
        # Trier par timestamp
        timeline.sort(key=lambda x: x['timestamp'])
        
        return {
            'total_errors': len(errors),
            'by_severity': by_severity,
            'by_type': by_type,
            'timeline': timeline,
            'first_error': timeline[0] if timeline else None,
            'last_error': timeline[-1] if timeline else None
        }
