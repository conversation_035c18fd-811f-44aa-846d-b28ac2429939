#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Streamlit pour l'Agent SQL Generator
"""

import streamlit as st
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(str(Path(__file__).parent))

from agents.sql_generator import SQLGenerator, SQLType
from utils.file_handler import FileHandler

# Configuration de la page
st.set_page_config(
    page_title="SQL Generator - LEONI",
    page_icon="🗄️",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# CSS Premium LEONI
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    /* Variables CSS */
    :root {
        --leoni-blue: #002857;
        --leoni-orange: #ff7514;
        --leoni-light-blue: #003366;
        --leoni-dark-blue: #001a33;
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-soft: 0 8px 32px rgba(0, 40, 87, 0.15);
        --shadow-strong: 0 16px 64px rgba(0, 40, 87, 0.25);
        --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Masquer les éléments Streamlit */
    .stDeployButton, #MainMenu, header[data-testid="stHeader"],
    footer, [data-testid="stToolbar"] {
        display: none !important;
        visibility: hidden !important;
    }

    /* Arrière-plan avec effet parallax */
    .stApp {
        background:
            linear-gradient(135deg, var(--leoni-blue) 0%, var(--leoni-light-blue) 50%, #004080 100%);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        position: relative;
        min-height: 100vh;
    }

    .stApp::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 20%, rgba(255, 117, 20, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(0, 40, 87, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, rgba(255, 117, 20, 0.05) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: backgroundFloat 20s ease-in-out infinite;
    }

    @keyframes backgroundFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(1deg); }
    }

    /* Navbar Premium avec Glassmorphism */
    .navbar {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 1.2rem 2.5rem;
        border-radius: 20px;
        margin: 1.5rem 0 3rem 0;
        box-shadow: var(--shadow-soft);
        border: 1px solid var(--glass-border);
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .navbar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 2s ease;
    }

    .navbar:hover::before {
        left: 100%;
    }

    .navbar-brand {
        font-size: 1.6rem;
        font-weight: 800;
        color: var(--leoni-blue);
        background: linear-gradient(135deg, var(--leoni-blue) 0%, var(--leoni-orange) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: -0.02em;
        position: relative;
        z-index: 2;
    }

    .navbar-nav {
        display: flex;
        gap: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .nav-link {
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        text-decoration: none;
        color: var(--leoni-blue);
        font-weight: 600;
        font-size: 0.95rem;
        transition: var(--transition-smooth);
        position: relative;
        overflow: hidden;
        border: 2px solid transparent;
    }

    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        opacity: 0;
        transition: var(--transition-smooth);
        z-index: -1;
    }

    .nav-link:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 117, 20, 0.3);
        border-color: var(--leoni-orange);
    }

    .nav-link:hover::before {
        opacity: 1;
    }

    .nav-link.active {
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        color: white;
        box-shadow: 0 6px 20px rgba(255, 117, 20, 0.4);
        transform: translateY(-1px);
    }

    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Titre principal avec effet premium */
    .main-header {
        font-size: 3.5rem;
        font-weight: 800;
        color: white;
        text-align: center;
        margin: 3rem 0;
        background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        letter-spacing: -0.03em;
        line-height: 1.1;
        animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
        position: relative;
    }

    .main-header::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        border-radius: 2px;
        animation: expandWidth 1s ease-out 1s both;
    }

    @keyframes expandWidth {
        from { width: 0; }
        to { width: 100px; }
    }

    /* Conteneurs avec Glassmorphism Premium */
    .stContainer > div, .element-container {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border-radius: 20px !important;
        padding: 2rem !important;
        margin: 1.5rem 0 !important;
        box-shadow: var(--shadow-soft) !important;
        border: 1px solid var(--glass-border) !important;
        transition: var(--transition-smooth) !important;
        position: relative !important;
        overflow: hidden !important;
        animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
    }

    .stContainer > div::before, .element-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--leoni-orange), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stContainer > div:hover, .element-container:hover {
        transform: translateY(-5px) !important;
        box-shadow: var(--shadow-strong) !important;
        border-color: rgba(255, 117, 20, 0.3) !important;
    }

    .stContainer > div:hover::before, .element-container:hover::before {
        opacity: 1;
    }

    /* Boutons Premium avec Micro-interactions */
    .stButton > button {
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 50px !important;
        padding: 1rem 2.5rem !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        font-size: 1.1rem !important;
        letter-spacing: 0.02em !important;
        box-shadow:
            0 8px 25px rgba(255, 117, 20, 0.3),
            0 4px 10px rgba(0, 0, 0, 0.1) !important;
        transition: var(--transition-smooth) !important;
        position: relative !important;
        overflow: hidden !important;
        text-transform: uppercase !important;
    }

    .stButton > button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
    }

    .stButton > button:hover {
        background: linear-gradient(135deg, #ff8f44 0%, var(--leoni-orange) 100%) !important;
        transform: translateY(-3px) scale(1.02) !important;
        box-shadow:
            0 12px 35px rgba(255, 117, 20, 0.4),
            0 8px 15px rgba(0, 0, 0, 0.15) !important;
    }

    .stButton > button:hover::before {
        left: 100%;
    }

    .stButton > button:active {
        transform: translateY(-1px) scale(1.01) !important;
        transition: all 0.1s ease !important;
    }

    .stButton > button:focus {
        outline: none !important;
        box-shadow:
            0 8px 25px rgba(255, 117, 20, 0.3),
            0 0 0 3px rgba(255, 117, 20, 0.2) !important;
    }

    /* Boîtes d'information */
    .info-box {
        padding: 1.5rem;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.95);
        border-left: 4px solid #002857;
        color: #002857;
        margin: 1rem 0;
        box-shadow: 0 4px 12px rgba(0, 40, 87, 0.2);
        line-height: 1.6;
    }

    /* Zone d'upload */
    .stFileUploader {
        background: rgba(255, 255, 255, 0.95) !important;
        border: 2px dashed #ff7514 !important;
        border-radius: 15px !important;
        padding: 2rem !important;
    }
    
    .stFileUploader:hover {
        border-color: #ff8f44 !important;
        background: rgba(255, 255, 255, 0.98) !important;
    }

    /* Code SQL */
    .sql-code {
        background: #1e1e1e;
        color: #d4d4d4;
        padding: 1.5rem;
        border-radius: 10px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        line-height: 1.4;
        overflow-x: auto;
        margin: 1rem 0;
        border-left: 4px solid #ff7514;
    }

    /* Métriques */
    [data-testid="metric-container"] {
        background: linear-gradient(135deg, #002857 0%, #003366 100%) !important;
        color: white !important;
        border-radius: 10px !important;
        padding: 1rem !important;
        box-shadow: 0 4px 12px rgba(0, 40, 87, 0.3) !important;
    }

    /* Animations d'entrée */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @keyframes shimmer {
        0% { background-position: -200px 0; }
        100% { background-position: calc(200px + 100%) 0; }
    }

    /* Typographie Premium */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        letter-spacing: -0.02em !important;
        color: var(--leoni-blue) !important;
    }

    p, div, span {
        font-family: 'Inter', sans-serif !important;
        line-height: 1.7 !important;
        color: rgba(0, 40, 87, 0.8) !important;
    }

    /* Effets de loading */
    .loading-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 2s infinite;
    }

    /* Scrollbar personnalisée */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(0, 40, 87, 0.1);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        border-radius: 4px;
        transition: background 0.3s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #ff8f44 0%, var(--leoni-orange) 100%);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .navbar {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
        }

        .navbar-nav {
            flex-wrap: wrap;
            justify-content: center;
        }

        .main-header {
            font-size: 2.5rem;
        }

        .stContainer > div {
            padding: 1rem !important;
            margin: 0.5rem 0 !important;
        }
    }
</style>
""", unsafe_allow_html=True)

def display_navbar():
    """Affiche la navbar de navigation avec transitions fluides"""
    st.markdown("""
    <div class="navbar">
        <div class="navbar-brand">🤖 LEONI AI Agents</div>
        <div class="navbar-nav">
            <a href="#" onclick="navigateToPage('http://localhost:8502')" class="nav-link">
                <span class="nav-icon">🔍</span>
                <span class="nav-text">Analyse d'Erreurs</span>
            </a>
            <a href="#" onclick="navigateToPage('http://localhost:8504')" class="nav-link active">
                <span class="nav-icon">🗄️</span>
                <span class="nav-text">SQL Generator</span>
            </a>
            <a href="#" onclick="showComingSoon('Agent 3')" class="nav-link">
                <span class="nav-icon">📊</span>
                <span class="nav-text">Agent 3</span>
            </a>
            <a href="#" onclick="showComingSoon('Agent 4')" class="nav-link">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">Agent 4</span>
            </a>
        </div>
    </div>

    <!-- Overlay de transition -->
    <div id="transition-overlay" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #002857 0%, #003366 100%);
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    ">
        <div style="
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 117, 20, 0.3);
            border-top: 4px solid #ff7514;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        "></div>
        <div style="
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            font-family: 'Inter', sans-serif;
        ">Chargement...</div>
    </div>

    <script>
        // Animation de rotation pour le loader
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .nav-link {
                display: flex !important;
                align-items: center !important;
                gap: 0.5rem !important;
            }

            .nav-icon {
                font-size: 1.1rem;
                transition: transform 0.3s ease;
            }

            .nav-link:hover .nav-icon {
                transform: scale(1.2) rotate(5deg);
            }

            .nav-text {
                font-size: 0.95rem;
            }
        `;
        document.head.appendChild(style);

        // Fonction de navigation avec transition fluide
        function navigateToPage(url) {
            const overlay = document.getElementById('transition-overlay');

            // Afficher l'overlay avec animation
            overlay.style.visibility = 'visible';
            overlay.style.opacity = '1';

            // Attendre l'animation puis naviguer
            setTimeout(() => {
                window.location.href = url;
            }, 300);
        }

        // Fonction pour les agents à venir
        function showComingSoon(agentName) {
            // Créer une notification élégante
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                padding: 2rem 3rem;
                border-radius: 20px;
                box-shadow: 0 16px 64px rgba(0, 40, 87, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.2);
                z-index: 10000;
                text-align: center;
                font-family: 'Inter', sans-serif;
                animation: popIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            `;

            notification.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 1rem;">🚀</div>
                <h3 style="color: #002857; margin-bottom: 0.5rem; font-weight: 700;">${agentName}</h3>
                <p style="color: rgba(0, 40, 87, 0.7); margin-bottom: 1.5rem;">Bientôt disponible !</p>
                <button onclick="this.parentElement.remove()" style="
                    background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%);
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 25px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">OK</button>
            `;

            // Ajouter l'animation CSS
            const animationStyle = document.createElement('style');
            animationStyle.textContent = `
                @keyframes popIn {
                    0% {
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.8);
                    }
                    100% {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }
                }
            `;
            document.head.appendChild(animationStyle);

            document.body.appendChild(notification);

            // Supprimer automatiquement après 3 secondes
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // Masquer l'overlay au chargement de la page
        window.addEventListener('load', () => {
            const overlay = document.getElementById('transition-overlay');
            overlay.style.opacity = '0';
            overlay.style.visibility = 'hidden';
        });
    </script>
    """, unsafe_allow_html=True)

def display_header():
    """Affiche l'en-tête de l'application"""
    st.markdown('<h1 class="main-header">🗄️ SQL Generator Agent</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    <div class="info-box">
        <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
            <div style="
                width: 48px; 
                height: 48px; 
                background: linear-gradient(135deg, #ff7514, #ff8f44); 
                border-radius: 12px; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                font-size: 1.5rem;
            ">🗄️</div>
            <div>
                <div style="font-weight: 600; font-size: 1.1rem; margin-bottom: 0.25rem;">Générateur SQL Intelligent</div>
                <div style="color: rgba(0, 40, 87, 0.7); font-size: 0.95rem;">Transforme vos spécifications en scripts SQL optimisés</div>
            </div>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1.5rem;">
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="color: #ff7514;">✨</span> Analyse automatique
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="color: #ff7514;">🎯</span> Multi-bases de données
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="color: #ff7514;">⚡</span> Scripts optimisés
            </div>
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="color: #ff7514;">🛠️</span> Données d'exemple
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def init_session_state():
    """Initialise l'état de session"""
    if 'sql_generator' not in st.session_state:
        st.session_state.sql_generator = SQLGenerator()
    if 'file_handler' not in st.session_state:
        st.session_state.file_handler = FileHandler()
    if 'generated_scripts' not in st.session_state:
        st.session_state.generated_scripts = None

def display_file_upload():
    """Affiche la zone d'upload de fichier"""
    st.subheader("📁 Upload du fichier de spécifications")
    
    uploaded_file = st.file_uploader(
        "Choisissez votre fichier de spécifications",
        type=['txt', 'md', 'doc', 'docx', 'pdf'],
        help="Formats supportés: TXT, MD, DOC, DOCX, PDF"
    )
    
    if uploaded_file is not None:
        return uploaded_file
    return None

def display_database_selection():
    """Affiche la sélection de base de données"""
    st.subheader("🗄️ Configuration de la base de données")
    
    col1, col2 = st.columns(2)
    
    with col1:
        database_type = st.selectbox(
            "Type de base de données",
            ["MySQL", "PostgreSQL", "SQL Server", "Oracle", "SQLite"],
            index=0,
            help="Sélectionnez le type de base de données cible"
        )
    
    with col2:
        include_sample_data = st.checkbox(
            "Inclure des données d'exemple",
            value=True,
            help="Génère des scripts INSERT avec des données d'exemple"
        )
    
    return database_type, include_sample_data

def analyze_specifications(file_content: str, database_type: str):
    """Analyse les spécifications et génère les scripts SQL"""
    
    with st.spinner("🔍 Analyse des spécifications en cours..."):
        # Parser les spécifications
        specs = st.session_state.sql_generator.parse_specifications(file_content)
        
        if not specs['tables']:
            st.warning("⚠️ Aucune table détectée dans les spécifications")
            return None
        
        # Générer les scripts CREATE TABLE
        create_scripts = st.session_state.sql_generator.generate_create_table_scripts(
            specs['tables'], database_type
        )
        
        # Générer les scripts INSERT si demandé
        insert_scripts = st.session_state.sql_generator.generate_insert_scripts(specs['tables'])
        
        return {
            'specs': specs,
            'create_scripts': create_scripts,
            'insert_scripts': insert_scripts,
            'database_type': database_type
        }

def display_results(results):
    """Affiche les résultats de l'analyse"""
    if not results:
        return
    
    specs = results['specs']
    create_scripts = results['create_scripts']
    insert_scripts = results['insert_scripts']
    database_type = results['database_type']
    
    # Métriques
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Tables détectées", len(specs['tables']))
    
    with col2:
        st.metric("Scripts CREATE", len(create_scripts))
    
    with col3:
        st.metric("Scripts INSERT", len(insert_scripts))
    
    with col4:
        st.metric("Base de données", database_type)
    
    # Onglets pour les différents types de scripts
    tab1, tab2, tab3 = st.tabs(["📋 Tables détectées", "🏗️ Scripts CREATE", "📝 Scripts INSERT"])
    
    with tab1:
        st.subheader("📋 Structure des tables détectées")
        
        for table in specs['tables']:
            with st.expander(f"Table: {table.name} ({len(table.columns)} colonnes)"):
                for col in table.columns:
                    col_info = f"**{col.name}** - {col.data_type.value}"
                    if col.length:
                        col_info += f"({col.length})"
                    if col.primary_key:
                        col_info += " 🔑 PK"
                    if not col.nullable:
                        col_info += " ⚠️ NOT NULL"
                    if col.auto_increment:
                        col_info += " 🔄 AUTO_INCREMENT"
                    
                    st.write(f"• {col_info}")
    
    with tab2:
        st.subheader("🏗️ Scripts CREATE TABLE")
        
        for script in create_scripts:
            with st.expander(f"CREATE TABLE {script.table_name}"):
                st.markdown(f"**Description:** {script.description}")
                st.markdown(f"**Temps d'exécution estimé:** {script.estimated_execution_time}")
                
                st.markdown(f"""
                <div class="sql-code">
{script.content}
                </div>
                """, unsafe_allow_html=True)
                
                # Bouton de copie
                if st.button(f"📋 Copier le script", key=f"copy_create_{script.table_name}"):
                    st.code(script.content, language='sql')
    
    with tab3:
        st.subheader("📝 Scripts INSERT (Données d'exemple)")
        
        for script in insert_scripts:
            with st.expander(f"INSERT INTO {script.table_name}"):
                st.markdown(f"**Description:** {script.description}")
                st.markdown(f"**Temps d'exécution estimé:** {script.estimated_execution_time}")
                
                st.markdown(f"""
                <div class="sql-code">
{script.content}
                </div>
                """, unsafe_allow_html=True)
                
                # Bouton de copie
                if st.button(f"📋 Copier le script", key=f"copy_insert_{script.table_name}"):
                    st.code(script.content, language='sql')

def main():
    """Fonction principale"""
    # Initialisation
    init_session_state()
    
    # Navigation
    display_navbar()
    
    # En-tête
    display_header()
    
    # Upload de fichier
    uploaded_file = display_file_upload()
    
    # Configuration
    database_type, include_sample_data = display_database_selection()
    
    # Analyse
    if uploaded_file is not None:
        if st.button("🚀 Générer les scripts SQL", type="primary"):
            try:
                # Lire le contenu du fichier
                file_content = st.session_state.file_handler.read_uploaded_file(uploaded_file)
                
                if file_content:
                    # Analyser et générer
                    results = analyze_specifications(file_content, database_type)
                    
                    if results and not include_sample_data:
                        # Supprimer les scripts INSERT si non demandés
                        results['insert_scripts'] = []
                    
                    st.session_state.generated_scripts = results
                    
                    if results:
                        st.success("✅ Scripts SQL générés avec succès !")
                    else:
                        st.error("❌ Erreur lors de la génération des scripts")
                else:
                    st.error("❌ Impossible de lire le fichier")
                    
            except Exception as e:
                st.error(f"❌ Erreur: {str(e)}")
    
    # Affichage des résultats
    if st.session_state.generated_scripts:
        st.markdown("---")
        display_results(st.session_state.generated_scripts)

if __name__ == "__main__":
    main()
