#!/usr/bin/env python3
"""
Script de test pour vérifier si la clé API OpenAI fonctionne
"""

import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents_system.config.config import DEFAULT_CONFIG
from agents_system.tools.chatgpt_client import ChatGPTClient

def test_api_key():
    """Test la clé API OpenAI"""
    print("🔑 Test de la clé API OpenAI...")
    print(f"Clé API: {DEFAULT_CONFIG.OPENAI_API_KEY[:20]}...")
    print(f"Modèle: {DEFAULT_CONFIG.OPENAI_MODEL}")

    try:
        # Test direct avec OpenAI
        import openai

        print("\n🔧 Test direct avec OpenAI...")
        openai.api_key = DEFAULT_CONFIG.OPENAI_API_KEY

        # Test simple avec l'API OpenAI directement
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",  # Utilisons un modèle plus simple pour le test
            messages=[
                {"role": "user", "content": "Dis simplement 'Test réussi' si tu reçois ce message."}
            ],
            max_tokens=50
        )

        if response and response.choices:
            content = response.choices[0].message.content
            print(f"✅ SUCCESS: La clé API fonctionne parfaitement !")
            print(f"📝 Réponse: {content}")
            return True
        else:
            print("❌ ERREUR: Réponse invalide")
            return False

    except Exception as e:
        print(f"❌ ERREUR: {str(e)}")

        # Test alternatif avec le nouveau client OpenAI
        try:
            print("\n🔄 Test avec le nouveau client OpenAI...")
            from openai import OpenAI

            client = OpenAI(api_key=DEFAULT_CONFIG.OPENAI_API_KEY)

            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "user", "content": "Dis simplement 'Test réussi' si tu reçois ce message."}
                ],
                max_tokens=50
            )

            if response and response.choices:
                content = response.choices[0].message.content
                print(f"✅ SUCCESS: La clé API fonctionne avec le nouveau client !")
                print(f"📝 Réponse: {content}")
                return True
            else:
                print("❌ ERREUR: Réponse invalide avec le nouveau client")
                return False

        except Exception as e2:
            print(f"❌ ERREUR avec le nouveau client: {str(e2)}")
            return False

if __name__ == "__main__":
    success = test_api_key()
    if success:
        print("\n🎉 La clé API est valide et fonctionnelle !")
    else:
        print("\n💥 La clé API ne fonctionne pas correctement.")
    
    sys.exit(0 if success else 1)
