"""
Démonstration du système d'agents sans ChatGPT
"""

import sys
import os
import logging
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(str(Path(__file__).parent))

from config.config import DEFAULT_CONFIG
from tools.code_analyzer import CodeAnalyzer
from tools.error_analyzer import <PERSON>rrorAnalyzer
from utils.file_handler import FileHandler
from utils.logger import setup_logger

def setup_logging():
    """Configure le système de logging"""
    os.makedirs(os.path.dirname(DEFAULT_CONFIG.LOG_FILE), exist_ok=True)
    setup_logger(
        name="agents_demo",
        log_file=DEFAULT_CONFIG.LOG_FILE,
        level=DEFAULT_CONFIG.LOG_LEVEL,
        format_str=DEFAULT_CONFIG.LOG_FORMAT
    )

def demo_analysis():
    """Démonstration de l'analyse sans ChatGPT"""
    
    # Configuration du logging
    setup_logging()
    logger = logging.getLogger("agents_demo")
    
    logger.info("Démarrage de la démonstration du système d'agents")
    
    # Fichiers d'exemple
    program_file = os.path.join(DEFAULT_CONFIG.EXAMPLES_DIR, "caofors program(1).txt")
    error_file = os.path.join(DEFAULT_CONFIG.EXAMPLES_DIR, "caofors_ORDER.11847.VSR1(3).error")
    
    if not os.path.exists(program_file) or not os.path.exists(error_file):
        print("❌ Fichiers d'exemple non trouvés")
        return
    
    print("🔍 DÉMONSTRATION DU SYSTÈME D'AGENTS")
    print("=" * 50)
    print()
    
    # Initialiser les outils
    file_handler = FileHandler()
    code_analyzer = CodeAnalyzer()
    error_analyzer = ErrorAnalyzer()
    
    # Lire les fichiers
    print("📁 Lecture des fichiers...")
    program_content = file_handler.read_file(program_file)
    error_content = file_handler.read_file(error_file)
    
    print(f"  ✅ Programme: {len(program_content)} caractères")
    print(f"  ✅ Erreurs: {len(error_content)} caractères")
    print()
    
    # Analyser les erreurs du fichier d'erreur
    print("🚨 ANALYSE DU FICHIER D'ERREUR")
    print("-" * 30)
    error_entries = error_analyzer.analyze_error_file(error_content)
    error_summary = error_analyzer.get_error_summary(error_entries)
    
    print(f"Total des erreurs détectées: {error_summary['total_errors']}")
    
    if error_summary['by_severity']:
        print("Répartition par sévérité:")
        for severity, count in error_summary['by_severity'].items():
            print(f"  • {severity}: {count}")
    
    if error_summary['by_type']:
        print("Répartition par type:")
        for error_type, count in error_summary['by_type'].items():
            print(f"  • {error_type}: {count}")
    
    print()
    
    # Détails des erreurs
    if error_entries:
        print("📋 DÉTAILS DES ERREURS")
        print("-" * 20)
        for i, error in enumerate(error_entries, 1):
            print(f"{i}. {error.message}")
            print(f"   Type: {error.error_type.value}")
            print(f"   Sévérité: {error.severity}")
            print(f"   Ligne {error.line_number}: {error.raw_line}")
            if error.timestamp:
                print(f"   Timestamp: {error.timestamp}")
            print()
    
    # Analyser le code
    print("🔍 ANALYSE DU CODE")
    print("-" * 18)
    code_errors = code_analyzer.analyze_code(program_content, "c")
    code_structure = code_analyzer.analyze_structure(program_content)
    
    print(f"Problèmes détectés dans le code: {len(code_errors)}")
    print()
    
    # Structure du code
    print("📊 STRUCTURE DU CODE")
    print("-" * 20)
    print(f"Lignes totales: {code_structure['total_lines']}")
    print(f"Lignes de code: {code_structure['code_lines']}")
    print(f"Lignes de commentaires: {code_structure['comment_lines']}")
    print(f"Lignes vides: {code_structure['blank_lines']}")
    print(f"Fonctions détectées: {len(code_structure['functions'])}")
    print(f"Includes détectés: {len(code_structure['includes'])}")
    print()
    
    # Problèmes de code (limité aux 10 premiers)
    if code_errors:
        print("⚠️  PROBLÈMES DÉTECTÉS (10 premiers)")
        print("-" * 35)
        for i, error in enumerate(code_errors[:10], 1):
            print(f"{i}. {error.message}")
            print(f"   Type: {error.error_type}")
            print(f"   Sévérité: {error.severity.value}")
            print(f"   Ligne {error.line_number}: {error.code_snippet[:80]}...")
            print(f"   Suggestion: {error.suggestion}")
            print()
    
    # Analyse spécifique de l'erreur principale
    print("🎯 ANALYSE SPÉCIFIQUE DE L'ERREUR PRINCIPALE")
    print("-" * 45)
    print("L'erreur principale détectée est:")
    print("  'ERROR task [ORDER_LOOP] locked (already runs). EXIT.'")
    print()
    print("🔍 DIAGNOSTIC:")
    print("  • Type: Erreur de ressource (RESOURCE_ERROR)")
    print("  • Cause: Un autre processus CAOFORS avec la tâche ORDER_LOOP est déjà en cours")
    print("  • Impact: Le programme ne peut pas démarrer")
    print()
    print("💡 SOLUTIONS RECOMMANDÉES:")
    print("  1. Vérifier les processus en cours: ps aux | grep caofors")
    print("  2. Arrêter le processus existant si nécessaire")
    print("  3. Vérifier les fichiers de verrouillage dans le répertoire temporaire")
    print("  4. Supprimer les fichiers .lock obsolètes si le processus n'existe plus")
    print()
    print("🛠️  COMMANDES SUGGÉRÉES:")
    print("  • Lister les processus: tasklist | findstr caofors")
    print("  • Arrêter un processus: taskkill /PID <pid> /F")
    print("  • Nettoyer les verrous: del /Q C:\\temp\\cao*\\*.lock")
    print()
    
    print("✅ Démonstration terminée avec succès!")
    logger.info("Démonstration terminée")

if __name__ == "__main__":
    demo_analysis()
