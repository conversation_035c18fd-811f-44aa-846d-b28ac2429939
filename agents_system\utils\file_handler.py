"""
Utilitaire de gestion des fichiers
"""

import os
import chardet
from typing import Op<PERSON>, <PERSON><PERSON>, List
from pathlib import Path
import streamlit as st

class FileHandler:
    """Gestionnaire de fichiers pour le système d'agents"""
    
    @staticmethod
    def read_file(file_path: str, encoding: Optional[str] = None) -> str:
        """
        Lit le contenu d'un fichier
        
        Args:
            file_path: Chemin vers le fichier
            encoding: Encodage du fichier (auto-détecté si None)
        
        Returns:
            Contenu du fichier
        
        Raises:
            FileNotFoundError: Si le fichier n'existe pas
            UnicodeDecodeError: Si l'encodage est incorrect
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Le fichier {file_path} n'existe pas")
        
        # Auto-détection de l'encodage si non spécifié
        if encoding is None:
            encoding = FileHandler.detect_encoding(file_path)
        
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return file.read()
        except UnicodeDecodeError:
            # Fallback vers utf-8 avec gestion des erreurs
            with open(file_path, 'r', encoding='utf-8', errors='replace') as file:
                return file.read()
    
    @staticmethod
    def detect_encoding(file_path: str) -> str:
        """
        Détecte l'encodage d'un fichier
        
        Args:
            file_path: Chemin vers le fichier
        
        Returns:
            Encodage détecté
        """
        try:
            with open(file_path, 'rb') as file:
                raw_data = file.read()
                result = chardet.detect(raw_data)
                return result['encoding'] or 'utf-8'
        except Exception:
            return 'utf-8'
    
    @staticmethod
    def write_file(file_path: str, content: str, encoding: str = 'utf-8') -> None:
        """
        Écrit du contenu dans un fichier
        
        Args:
            file_path: Chemin vers le fichier
            content: Contenu à écrire
            encoding: Encodage du fichier
        """
        # Créer le répertoire si nécessaire
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding=encoding) as file:
            file.write(content)
    
    @staticmethod
    def get_file_info(file_path: str) -> dict:
        """
        Récupère les informations d'un fichier
        
        Args:
            file_path: Chemin vers le fichier
        
        Returns:
            Dictionnaire avec les informations du fichier
        """
        if not os.path.exists(file_path):
            return {}
        
        stat = os.stat(file_path)
        path_obj = Path(file_path)
        
        return {
            'name': path_obj.name,
            'extension': path_obj.suffix,
            'size': stat.st_size,
            'modified': stat.st_mtime,
            'created': stat.st_ctime,
            'is_file': path_obj.is_file(),
            'is_dir': path_obj.is_dir(),
            'absolute_path': str(path_obj.absolute())
        }
    
    @staticmethod
    def validate_file_type(file_path: str, allowed_extensions: List[str]) -> bool:
        """
        Valide le type d'un fichier
        
        Args:
            file_path: Chemin vers le fichier
            allowed_extensions: Liste des extensions autorisées
        
        Returns:
            True si le type est valide
        """
        path_obj = Path(file_path)
        return path_obj.suffix.lower() in [ext.lower() for ext in allowed_extensions]
    
    @staticmethod
    def count_lines(file_path: str) -> int:
        """
        Compte le nombre de lignes dans un fichier
        
        Args:
            file_path: Chemin vers le fichier
        
        Returns:
            Nombre de lignes
        """
        try:
            content = FileHandler.read_file(file_path)
            return len(content.splitlines())
        except Exception:
            return 0

    @staticmethod
    def read_uploaded_file(uploaded_file) -> Optional[str]:
        """
        Lit le contenu d'un fichier uploadé via Streamlit

        Args:
            uploaded_file: Fichier uploadé via st.file_uploader

        Returns:
            Contenu du fichier ou None si erreur
        """
        try:
            # Lire le contenu en bytes
            content_bytes = uploaded_file.read()

            # Détecter l'encodage
            detected = chardet.detect(content_bytes)
            encoding = detected.get('encoding', 'utf-8')

            # Décoder le contenu
            try:
                content = content_bytes.decode(encoding)
            except UnicodeDecodeError:
                # Fallback vers utf-8 avec gestion des erreurs
                content = content_bytes.decode('utf-8', errors='replace')

            return content

        except Exception as e:
            st.error(f"Erreur lors de la lecture du fichier: {str(e)}")
            return None
