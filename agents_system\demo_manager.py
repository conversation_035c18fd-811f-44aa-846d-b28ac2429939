#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LEONI AI Agent - Démonstration Manager
Version professionnelle pour présentation en entreprise
"""

import streamlit as st
import sys
import os
from pathlib import Path
import time

# Ajouter le répertoire parent au path
sys.path.append(str(Path(__file__).parent))

# Configuration de la page
st.set_page_config(
    page_title="LEONI AI Agent - Analyse d'Erreurs",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# CSS Professionnel pour Manager
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Masquer les éléments Streamlit */
    .stDeployButton, #MainMenu, header[data-testid="stHeader"], 
    footer, [data-testid="stToolbar"] {
        display: none !important;
    }

    /* Design professionnel LEONI */
    .stApp {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-family: 'Inter', sans-serif;
    }
    
    /* Header corporate */
    .corporate-header {
        background: linear-gradient(135deg, #002857 0%, #003366 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0, 40, 87, 0.15);
    }
    
    .corporate-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .corporate-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 400;
    }
    
    .leoni-badge {
        background: #ff7514;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-left: auto;
    }

    /* Conteneurs professionnels */
    .demo-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin: 1rem 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }
    
    .demo-container:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    /* Métriques KPI */
    .kpi-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }
    
    .kpi-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }
    
    .kpi-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .kpi-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: #002857;
        margin-bottom: 0.5rem;
    }
    
    .kpi-label {
        font-size: 0.9rem;
        color: #64748b;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Boutons professionnels */
    .stButton > button {
        background: linear-gradient(135deg, #002857 0%, #003366 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 0.75rem 2rem !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 600 !important;
        font-size: 1rem !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 8px rgba(0, 40, 87, 0.2) !important;
    }
    
    .stButton > button:hover {
        background: linear-gradient(135deg, #003366 0%, #004080 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 15px rgba(0, 40, 87, 0.3) !important;
    }

    /* Upload zones professionnelles */
    .upload-zone {
        border: 2px dashed #cbd5e1;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        background: #f8fafc;
        transition: all 0.3s ease;
    }
    
    .upload-zone:hover {
        border-color: #002857;
        background: #f1f5f9;
    }

    /* Messages de statut */
    .status-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
        font-weight: 500;
    }
    
    .status-info {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
        font-weight: 500;
    }

    /* Résultats professionnels */
    .result-section {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
        border-left: 4px solid #ff7514;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    
    .result-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #002857;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Progress bar */
    .progress-container {
        background: #e2e8f0;
        border-radius: 10px;
        height: 8px;
        margin: 1rem 0;
        overflow: hidden;
    }
    
    .progress-bar {
        background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%);
        height: 100%;
        border-radius: 10px;
        transition: width 2s ease;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .corporate-title {
            font-size: 2rem;
        }
        
        .kpi-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session"""
    if 'code_analyzer' not in st.session_state:
        from tools.code_analyzer import CodeAnalyzer
        st.session_state.code_analyzer = CodeAnalyzer()
    
    if 'error_analyzer' not in st.session_state:
        from tools.error_analyzer import ErrorAnalyzer
        st.session_state.error_analyzer = ErrorAnalyzer()

def display_corporate_header():
    """Affiche l'en-tête corporate"""
    st.markdown("""
    <div class="corporate-header">
        <div class="corporate-title">
            🔍 LEONI AI Agent
            <div class="leoni-badge">DEMO</div>
        </div>
        <div class="corporate-subtitle">
            Solution d'Intelligence Artificielle pour l'Analyse Automatique d'Erreurs
        </div>
    </div>
    """, unsafe_allow_html=True)

def display_value_proposition():
    """Affiche la proposition de valeur"""
    st.markdown("""
    <div class="demo-container">
        <h3>🎯 Valeur Ajoutée pour LEONI</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 1.5rem;">
            <div>
                <h4>⚡ Gain de Temps</h4>
                <p>Réduction de 80% du temps d'analyse des erreurs de production</p>
            </div>
            <div>
                <h4>🎯 Précision</h4>
                <p>Détection automatique et classification intelligente des problèmes</p>
            </div>
            <div>
                <h4>💡 Recommandations</h4>
                <p>Solutions contextuelles basées sur l'analyse du code et des logs</p>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def display_file_upload_demo():
    """Section d'upload pour la démo"""
    st.markdown("""
    <div class="demo-container">
        <h3>📁 Démonstration - Upload de Fichiers</h3>
        <p>Uploadez vos fichiers de code et d'erreur pour une analyse automatique</p>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**📄 Fichier Programme**")
        program_file = st.file_uploader(
            "Code source, script, ou programme",
            help="Formats supportés: .c, .cpp, .py, .js, .java, etc.",
            key="demo_program"
        )
        
        if program_file:
            st.markdown(f'<div class="status-success">✅ Fichier chargé: {program_file.name}</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown("**🚨 Fichier d'Erreur**")
        error_file = st.file_uploader(
            "Log d'erreur, trace, ou rapport",
            help="Formats supportés: .log, .error, .txt, etc.",
            key="demo_error"
        )
        
        if error_file:
            st.markdown(f'<div class="status-success">✅ Fichier chargé: {error_file.name}</div>', unsafe_allow_html=True)
    
    return program_file, error_file

def display_analysis_progress():
    """Affiche une barre de progression pour l'analyse"""
    progress_placeholder = st.empty()
    
    steps = [
        "🔍 Lecture des fichiers...",
        "🧠 Analyse du code source...",
        "🚨 Traitement des erreurs...",
        "📊 Génération des métriques...",
        "💡 Création des recommandations...",
        "✅ Finalisation du rapport..."
    ]
    
    for i, step in enumerate(steps):
        progress = (i + 1) / len(steps) * 100
        progress_placeholder.markdown(f"""
        <div class="demo-container">
            <div style="margin-bottom: 1rem;">{step}</div>
            <div class="progress-container">
                <div class="progress-bar" style="width: {progress}%"></div>
            </div>
            <div style="text-align: center; color: #64748b; font-size: 0.9rem;">{progress:.0f}% terminé</div>
        </div>
        """, unsafe_allow_html=True)
        time.sleep(0.8)  # Simulation du temps de traitement
    
    progress_placeholder.empty()

def display_kpi_dashboard(results):
    """Affiche un dashboard KPI professionnel"""
    st.markdown("""
    <div class="demo-container">
        <h3>📊 Tableau de Bord - Métriques Clés</h3>
        <div class="kpi-container">
            <div class="kpi-card">
                <div class="kpi-value">{}</div>
                <div class="kpi-label">Erreurs Détectées</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">{}</div>
                <div class="kpi-label">Problèmes Code</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">{}</div>
                <div class="kpi-label">Lignes Analysées</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">{}</div>
                <div class="kpi-label">Fonctions</div>
            </div>
        </div>
    </div>
    """.format(
        len(results['error_entries']),
        len(results['code_errors']),
        results['code_structure']['total_lines'],
        len(results['code_structure']['functions'])
    ), unsafe_allow_html=True)

def display_executive_summary(results):
    """Affiche un résumé exécutif pour le manager"""
    st.markdown("""
    <div class="demo-container">
        <h3>📋 Résumé Exécutif</h3>
    </div>
    """, unsafe_allow_html=True)
    
    # Analyse de l'erreur principale
    if results['error_entries']:
        main_error = results['error_entries'][0]
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.markdown(f"""
            <div class="result-section">
                <div class="result-title">🎯 Problème Principal Identifié</div>
                <p><strong>Erreur:</strong> {main_error.message}</p>
                <p><strong>Type:</strong> {main_error.error_type.value}</p>
                <p><strong>Impact:</strong> {"Critique - Blocage de production" if "locked" in main_error.message.lower() else "Modéré - Erreur d'exécution"}</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            severity_color = "#dc2626" if main_error.severity == "ERROR" else "#f59e0b"
            st.markdown(f"""
            <div class="result-section">
                <div class="result-title">⚠️ Sévérité</div>
                <div style="background: {severity_color}; color: white; padding: 1rem; border-radius: 8px; text-align: center; font-weight: 600;">
                    {main_error.severity}
                </div>
            </div>
            """, unsafe_allow_html=True)

def display_recommendations(results):
    """Affiche les recommandations business"""
    st.markdown("""
    <div class="demo-container">
        <h3>💡 Recommandations Stratégiques</h3>
    </div>
    """, unsafe_allow_html=True)
    
    if results['error_entries']:
        main_error = results['error_entries'][0]
        
        if "locked" in main_error.message.lower():
            st.markdown("""
            <div class="result-section">
                <div class="result-title">🔧 Actions Immédiates</div>
                <ol>
                    <li><strong>Court terme:</strong> Vérifier et arrêter les processus en conflit</li>
                    <li><strong>Moyen terme:</strong> Implémenter un système de gestion des verrous</li>
                    <li><strong>Long terme:</strong> Automatiser la détection et résolution de ces conflits</li>
                </ol>
            </div>
            """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="result-section">
            <div class="result-title">📈 ROI Estimé</div>
            <ul>
                <li><strong>Temps de résolution:</strong> Réduction de 4h à 15 minutes</li>
                <li><strong>Coût évité:</strong> ~2000€ par incident</li>
                <li><strong>Disponibilité:</strong> +99.5% uptime des systèmes critiques</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

def main():
    """Fonction principale pour la démo manager"""
    
    # Initialisation
    init_session_state()
    
    # Header corporate
    display_corporate_header()
    
    # Proposition de valeur
    display_value_proposition()
    
    # Upload et démo
    program_file, error_file = display_file_upload_demo()
    
    # Bouton d'analyse
    if program_file and error_file:
        if st.button("🚀 Lancer l'Analyse IA", type="primary"):
            
            # Barre de progression
            display_analysis_progress()
            
            # Lecture des fichiers
            try:
                program_content = program_file.getvalue().decode('utf-8')
            except:
                program_content = program_file.getvalue().decode('latin-1')
                
            try:
                error_content = error_file.getvalue().decode('utf-8')
            except:
                error_content = error_file.getvalue().decode('latin-1')
            
            # Analyse
            error_entries = st.session_state.error_analyzer.analyze_error_file(error_content)
            error_summary = st.session_state.error_analyzer.get_error_summary(error_entries)
            code_errors = st.session_state.code_analyzer.analyze_code(program_content, "c")
            code_structure = st.session_state.code_analyzer.analyze_structure(program_content)
            
            results = {
                'error_entries': error_entries,
                'error_summary': error_summary,
                'code_errors': code_errors,
                'code_structure': code_structure
            }
            
            # Affichage des résultats
            st.markdown('<div class="status-info">✅ Analyse terminée avec succès - Rapport généré</div>', unsafe_allow_html=True)
            
            # Dashboard KPI
            display_kpi_dashboard(results)
            
            # Résumé exécutif
            display_executive_summary(results)
            
            # Recommandations
            display_recommendations(results)
            
            # Call to action
            st.markdown("""
            <div class="demo-container" style="text-align: center; background: linear-gradient(135deg, #002857 0%, #003366 100%); color: white;">
                <h3>🚀 Prêt pour le Déploiement</h3>
                <p>Cette solution peut être intégrée dans votre infrastructure LEONI en 2 semaines</p>
                <p><strong>Contact:</strong> Équipe IA - extension 1234</p>
            </div>
            """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
