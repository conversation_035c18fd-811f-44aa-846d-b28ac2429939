"""
Utilitaire de logging pour le système d'agents
"""

import logging
import os
from typing import Optional

def setup_logger(
    name: str,
    log_file: Optional[str] = None,
    level: str = "INFO",
    format_str: Optional[str] = None
) -> logging.Logger:
    """
    Configure et retourne un logger
    
    Args:
        name: Nom du logger
        log_file: Chemin vers le fichier de log (optionnel)
        level: Niveau de logging (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_str: Format des messages de log
    
    Returns:
        Logger configuré
    """
    
    # Format par défaut
    if format_str is None:
        format_str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Créer le logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Éviter la duplication des handlers
    if logger.handlers:
        return logger
    
    # Formatter
    formatter = logging.Formatter(format_str)
    
    # Handler pour la console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Handler pour le fichier (si spécifié)
    if log_file:
        # Créer le répertoire si nécessaire
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, level.upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """
    Récupère un logger existant
    
    Args:
        name: Nom du logger
    
    Returns:
        Logger
    """
    return logging.getLogger(name)
