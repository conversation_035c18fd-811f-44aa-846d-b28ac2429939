#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application Style Original - LEONI AI Agent
Version console simple comme le demo.py original
"""

import streamlit as st
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(str(Path(__file__).parent))

# Configuration de la page
st.set_page_config(
    page_title="LEONI AI Agent",
    page_icon="🤖",
    layout="centered"
)

# CSS Minimal - Style Original
st.markdown("""
<style>
    /* Masquer les éléments Streamlit */
    .stDeployButton, #MainMenu, header[data-testid="stHeader"], 
    footer, [data-testid="stToolbar"] {
        display: none !important;
    }

    /* Style simple et épuré */
    .stApp {
        background-color: #f8f9fa;
        font-family: 'Courier New', monospace;
    }
    
    /* Titre simple */
    h1 {
        color: #333;
        font-family: 'Courier New', monospace;
        border-bottom: 2px solid #333;
        padding-bottom: 10px;
    }
    
    /* Code blocks */
    .stCode {
        background-color: #f1f1f1;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    /* Messages */
    .stSuccess, .stError, .stInfo, .stWarning {
        font-family: 'Courier New', monospace;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session"""
    if 'code_analyzer' not in st.session_state:
        from tools.code_analyzer import CodeAnalyzer
        st.session_state.code_analyzer = CodeAnalyzer()
    
    if 'error_analyzer' not in st.session_state:
        from tools.error_analyzer import ErrorAnalyzer
        st.session_state.error_analyzer = ErrorAnalyzer()

def main():
    """Fonction principale - Style original du demo.py"""
    
    # Initialisation
    init_session_state()
    
    # Titre simple
    st.title("🔍 DÉMONSTRATION DU SYSTÈME D'AGENTS")
    st.write("=" * 50)
    
    # Section de téléchargement simple
    st.header("📁 Lecture des fichiers...")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Programme")
        program_file = st.file_uploader("Fichier programme", key="program")
        
        if program_file:
            file_size = len(program_file.getvalue())
            st.write(f"✅ Programme: {file_size} caractères")
    
    with col2:
        st.subheader("Erreurs") 
        error_file = st.file_uploader("Fichier d'erreur", key="error")
        
        if error_file:
            file_size = len(error_file.getvalue())
            st.write(f"✅ Erreurs: {file_size} caractères")
    
    st.write("")
    
    # Analyse si les deux fichiers sont présents
    if program_file and error_file:
        if st.button("Lancer l'analyse"):
            
            # Lire les contenus
            try:
                program_content = program_file.getvalue().decode('utf-8')
            except:
                program_content = program_file.getvalue().decode('latin-1')
                
            try:
                error_content = error_file.getvalue().decode('utf-8')
            except:
                error_content = error_file.getvalue().decode('latin-1')
            
            # Analyse des erreurs du fichier d'erreur
            st.header("🚨 ANALYSE DU FICHIER D'ERREUR")
            st.write("-" * 30)
            
            error_entries = st.session_state.error_analyzer.analyze_error_file(error_content)
            error_summary = st.session_state.error_analyzer.get_error_summary(error_entries)
            
            st.write(f"Total des erreurs détectées: {error_summary['total_errors']}")
            
            if error_summary['by_severity']:
                st.write("Répartition par sévérité:")
                for severity, count in error_summary['by_severity'].items():
                    st.write(f"  • {severity}: {count}")
            
            if error_summary['by_type']:
                st.write("Répartition par type:")
                for error_type, count in error_summary['by_type'].items():
                    st.write(f"  • {error_type}: {count}")
            
            st.write("")
            
            # Détails des erreurs
            if error_entries:
                st.subheader("📋 DÉTAILS DES ERREURS")
                st.write("-" * 20)
                
                for i, error in enumerate(error_entries[:5], 1):  # Limiter à 5 pour l'affichage
                    st.write(f"{i}. {error.message}")
                    st.write(f"   Type: {error.error_type.value}")
                    st.write(f"   Sévérité: {error.severity}")
                    st.write(f"   Ligne {error.line_number}: {error.raw_line}")
                    if error.timestamp:
                        st.write(f"   Timestamp: {error.timestamp}")
                    st.write("")
            
            # Analyser le code
            st.header("🔍 ANALYSE DU CODE")
            st.write("-" * 18)
            
            code_errors = st.session_state.code_analyzer.analyze_code(program_content, "c")
            code_structure = st.session_state.code_analyzer.analyze_structure(program_content)
            
            st.write(f"Problèmes détectés dans le code: {len(code_errors)}")
            st.write("")
            
            # Structure du code
            st.subheader("📊 STRUCTURE DU CODE")
            st.write("-" * 20)
            st.write(f"Lignes totales: {code_structure['total_lines']}")
            st.write(f"Lignes de code: {code_structure['code_lines']}")
            st.write(f"Lignes de commentaires: {code_structure['comment_lines']}")
            st.write(f"Lignes vides: {code_structure['blank_lines']}")
            st.write(f"Fonctions détectées: {len(code_structure['functions'])}")
            st.write(f"Includes détectés: {len(code_structure['includes'])}")
            st.write("")
            
            # Problèmes de code (limité aux 5 premiers)
            if code_errors:
                st.subheader("⚠️ PROBLÈMES DÉTECTÉS (5 premiers)")
                st.write("-" * 35)
                
                for i, error in enumerate(code_errors[:5], 1):
                    st.write(f"{i}. {error.message}")
                    st.write(f"   Type: {error.error_type}")
                    st.write(f"   Sévérité: {error.severity.value}")
                    st.write(f"   Ligne {error.line_number}: {error.code_snippet[:80]}...")
                    st.write(f"   Suggestion: {error.suggestion}")
                    st.write("")
            
            # Analyse spécifique de l'erreur principale
            st.header("🎯 ANALYSE SPÉCIFIQUE DE L'ERREUR PRINCIPALE")
            st.write("-" * 45)
            
            if error_entries:
                main_error = error_entries[0]
                st.write("L'erreur principale détectée est:")
                st.write(f"  '{main_error.message}'")
                st.write("")
                
                st.write("🔍 DIAGNOSTIC:")
                st.write(f"  • Type: {main_error.error_type.value}")
                
                if "locked" in main_error.message.lower():
                    st.write("  • Cause: Un autre processus CAOFORS avec la tâche ORDER_LOOP est déjà en cours")
                    st.write("  • Impact: Le programme ne peut pas démarrer")
                    st.write("")
                    
                    st.write("💡 SOLUTIONS RECOMMANDÉES:")
                    st.write("  1. Vérifier les processus en cours: ps aux | grep caofors")
                    st.write("  2. Arrêter le processus existant si nécessaire")
                    st.write("  3. Vérifier les fichiers de verrouillage dans le répertoire temporaire")
                    st.write("  4. Supprimer les fichiers .lock obsolètes si le processus n'existe plus")
                    st.write("")
                    
                    st.write("🛠️ COMMANDES SUGGÉRÉES:")
                    st.write("  • Lister les processus: tasklist | findstr caofors")
                    st.write("  • Arrêter un processus: taskkill /PID <pid> /F")
                    st.write("  • Nettoyer les verrous: del /Q C:\\temp\\cao*\\*.lock")
                else:
                    st.write(f"  • Cause: {main_error.message}")
                    st.write("  • Impact: Erreur dans l'exécution du programme")
                    st.write("")
                    
                    st.write("💡 SOLUTIONS RECOMMANDÉES:")
                    st.write("  1. Vérifier la syntaxe du code")
                    st.write("  2. Contrôler les dépendances")
                    st.write("  3. Examiner les logs détaillés")
                    st.write("  4. Tester avec des données simplifiées")
            
            st.write("")
            st.success("✅ Démonstration terminée avec succès!")

if __name__ == "__main__":
    main()
