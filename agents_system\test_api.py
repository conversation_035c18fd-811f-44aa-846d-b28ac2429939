"""
Script de test pour vérifier la clé API OpenAI
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.chatgpt_client import ChatGPTClient
from config.config import DEFAULT_CONFIG

def test_api_key():
    """Test de la clé API"""
    print("Test de la clé API OpenAI...")
    print(f"Clé API: {DEFAULT_CONFIG.OPENAI_API_KEY[:20]}...")
    print(f"Modèle: {DEFAULT_CONFIG.OPENAI_MODEL}")
    
    try:
        client = ChatGPTClient(
            api_key=DEFAULT_CONFIG.OPENAI_API_KEY,
            model=DEFAULT_CONFIG.OPENAI_MODEL
        )
        
        print("Client créé avec succès")
        
        # Test simple
        response = client.send_message(
            message="Bonjour, pouvez-vous me dire que vous fonctionnez ?",
            max_tokens=50
        )
        
        print("✅ Test réussi !")
        print(f"Réponse: {response.content}")
        print(f"Tokens utilisés: {response.usage}")
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")

if __name__ == "__main__":
    test_api_key()
