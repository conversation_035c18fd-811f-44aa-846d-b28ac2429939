Spécifications de Base de Données - Système de Gestion LEONI
===========================================================

Base de données: MySQL

Table Users
-----------
- id: INT PRIMARY KEY AUTO_INCREMENT
- username: VA<PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE
- email: VARCHAR(100) NOT NULL UNIQUE
- password_hash: VARCHAR(255) NOT NULL
- first_name: VARCHAR(50)
- last_name: VA<PERSON><PERSON><PERSON>(50)
- created_at: DATETIME DEFAULT CURRENT_TIMESTAMP
- updated_at: DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
- is_active: BOOLEAN DEFAULT TRUE

Table Departments
-----------------
- id: INT PRIMARY KEY AUTO_INCREMENT
- name: VARCHAR(100) NOT NULL UNIQUE
- description: TEXT
- manager_id: INT
- budget: DECIMAL(15,2)
- created_at: DATETIME DEFAULT CURRENT_TIMESTAMP

Table Employees
---------------
- id: INT PRIMARY KEY AUTO_INCREMENT
- employee_code: VARCHAR(20) NOT NULL UNIQUE
- user_id: INT NOT NULL
- department_id: INT NOT NULL
- position: VARCHAR(100)
- salary: DECIMAL(10,2)
- hire_date: DATE NOT NULL
- status: VARCHAR(20) DEFAULT 'ACTIVE'
- manager_id: INT

Table Projects
--------------
- id: INT PRIMARY KEY AUTO_INCREMENT
- name: VARCHAR(200) NOT NULL
- description: TEXT
- start_date: DATE
- end_date: DATE
- budget: DECIMAL(15,2)
- status: VARCHAR(20) DEFAULT 'PLANNING'
- department_id: INT NOT NULL
- project_manager_id: INT

Table Tasks
-----------
- id: INT PRIMARY KEY AUTO_INCREMENT
- title: VARCHAR(200) NOT NULL
- description: TEXT
- project_id: INT NOT NULL
- assigned_to: INT
- priority: VARCHAR(10) DEFAULT 'MEDIUM'
- status: VARCHAR(20) DEFAULT 'TODO'
- estimated_hours: DECIMAL(5,2)
- actual_hours: DECIMAL(5,2)
- due_date: DATE
- created_at: DATETIME DEFAULT CURRENT_TIMESTAMP
- completed_at: DATETIME

Relations
---------
- Employees.user_id references Users.id
- Employees.department_id references Departments.id
- Employees.manager_id references Employees.id
- Departments.manager_id references Employees.id
- Projects.department_id references Departments.id
- Projects.project_manager_id references Employees.id
- Tasks.project_id references Projects.id
- Tasks.assigned_to references Employees.id

Indexes
-------
- INDEX idx_users_email ON Users(email)
- INDEX idx_employees_code ON Employees(employee_code)
- INDEX idx_tasks_project ON Tasks(project_id)
- INDEX idx_tasks_assigned ON Tasks(assigned_to)
- INDEX idx_projects_department ON Projects(department_id)

Notes
-----
- Toutes les tables doivent avoir des timestamps de création et modification
- Les clés étrangères doivent être définies avec contraintes
- Les champs email doivent être validés
- Les statuts doivent être contrôlés par des ENUM ou CHECK constraints
