"""
Classe de base pour tous les agents
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class AgentResult:
    """Résultat d'exécution d'un agent"""
    success: bool
    data: Any
    message: str
    execution_time: float
    errors: Optional[list] = None

class BaseAgent(ABC):
    """Classe de base pour tous les agents"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        Initialise l'agent
        
        Args:
            name: Nom de l'agent
            config: Configuration de l'agent
        """
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"agents.{name}")
        
        # Propriétés de l'agent depuis la config
        self.role = config.get('role', 'Agent générique')
        self.goal = config.get('goal', 'Traitement générique')
        self.prompt = config.get('prompt', '')
        self.max_iterations = config.get('max_iterations', 3)
        self.timeout = config.get('timeout', 300)
        
        self.logger.info(f"Agent {self.name} initialisé")
        self.logger.info(f"Rôle: {self.role}")
        self.logger.info(f"Objectif: {self.goal}")
    
    @abstractmethod
    def execute(self, *args, **kwargs) -> AgentResult:
        """
        Exécute la tâche principale de l'agent
        
        Returns:
            Résultat de l'exécution
        """
        pass
    
    def validate_inputs(self, *args, **kwargs) -> bool:
        """
        Valide les entrées de l'agent
        
        Returns:
            True si les entrées sont valides
        """
        return True
    
    def log_info(self, message: str):
        """Log un message d'information"""
        self.logger.info(f"[{self.name}] {message}")
    
    def log_warning(self, message: str):
        """Log un avertissement"""
        self.logger.warning(f"[{self.name}] {message}")
    
    def log_error(self, message: str):
        """Log une erreur"""
        self.logger.error(f"[{self.name}] {message}")
    
    def get_info(self) -> Dict[str, Any]:
        """
        Retourne les informations de l'agent
        
        Returns:
            Informations de l'agent
        """
        return {
            'name': self.name,
            'role': self.role,
            'goal': self.goal,
            'max_iterations': self.max_iterations,
            'timeout': self.timeout
        }
