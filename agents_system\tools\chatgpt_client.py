"""
Client pour l'intégration avec ChatGPT 4.1
"""

import openai
import json
import time
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from .response_formatter import ResponseFormatter

# Configuration du logger
logger = logging.getLogger(__name__)

@dataclass
class ChatGPTResponse:
    """Réponse de ChatGPT"""
    content: str
    usage: Dict[str, int]
    model: str
    finish_reason: str
    response_time: float

class ChatGPTClient:
    """Client pour interagir avec ChatGPT 4.1"""
    
    def __init__(self, api_key: str, model: str = "gpt-4-turbo-preview"):
        """
        Initialise le client ChatGPT

        Args:
            api_key: Clé API OpenAI
            model: Modèle à utiliser
        """
        # S'assurer que la clé API est en UTF-8
        if isinstance(api_key, str):
            self.api_key = api_key.encode('utf-8').decode('utf-8')
        else:
            self.api_key = str(api_key)

        self.model = model
        self.formatter = ResponseFormatter()

        # Initialiser le client OpenAI avec gestion d'encodage
        try:
            self.client = openai.OpenAI(api_key=self.api_key)
        except Exception as e:
            logger.error(f"Erreur d'initialisation du client OpenAI: {e}")
            raise
    
    def send_message(
        self,
        message: str,
        system_prompt: Optional[str] = None,
        max_tokens: int = 4000,
        temperature: float = 0.1,
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> ChatGPTResponse:
        """
        Envoie un message à ChatGPT
        
        Args:
            message: Message à envoyer
            system_prompt: Prompt système (optionnel)
            max_tokens: Nombre maximum de tokens
            temperature: Température pour la génération
            conversation_history: Historique de conversation
        
        Returns:
            Réponse de ChatGPT
        """
        start_time = time.time()
        
        # Construire les messages
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # Ajouter l'historique de conversation
        if conversation_history:
            messages.extend(conversation_history)
        
        # Ajouter le message actuel
        messages.append({"role": "user", "content": message})
        
        try:
            # S'assurer que tous les messages sont en UTF-8
            encoded_messages = []
            for msg in messages:
                encoded_msg = {
                    "role": msg["role"],
                    "content": msg["content"].encode('utf-8').decode('utf-8') if isinstance(msg["content"], str) else str(msg["content"])
                }
                encoded_messages.append(encoded_msg)

            response = self.client.chat.completions.create(
                model=self.model,
                messages=encoded_messages,
                max_tokens=max_tokens,
                temperature=temperature
            )

            response_time = time.time() - start_time

            return ChatGPTResponse(
                content=response.choices[0].message.content,
                usage=response.usage.model_dump(),
                model=response.model,
                finish_reason=response.choices[0].finish_reason,
                response_time=response_time
            )

        except UnicodeEncodeError as e:
            logger.error(f"Erreur d'encodage Unicode: {e}")
            raise Exception(f"Erreur d'encodage lors de l'appel à ChatGPT: {str(e)}")
        except Exception as e:
            logger.error(f"Erreur ChatGPT: {e}")
            raise Exception(f"Erreur lors de l'appel à ChatGPT: {str(e)}")
    
    def analyze_code_errors(
        self,
        program_content: str,
        error_content: str,
        agent_prompt: str
    ) -> ChatGPTResponse:
        """
        Analyse les erreurs de code avec ChatGPT
        
        Args:
            program_content: Contenu du fichier programme
            error_content: Contenu du fichier d'erreur
            agent_prompt: Prompt de l'agent
        
        Returns:
            Analyse des erreurs
        """
        
        analysis_prompt = f"""
        Voici un fichier de programme CAOFORS et son fichier d'erreur associé que tu dois analyser en détail.

        FICHIER PROGRAMME:
        ```
        {program_content[:8000]}  # Limiter pour éviter de dépasser les tokens
        ```

        FICHIER D'ERREUR:
        ```
        {error_content}
        ```

        CONTEXTE: Il s'agit d'un système CAOFORS avec du SQL embarqué. Les erreurs peuvent concerner :
        - Des verrous de tâches (task locked)
        - Des erreurs SQL (SQLCODE)
        - Des problèmes de permissions
        - Des conflits de processus
        - Des erreurs de base de données

        INSTRUCTIONS SPÉCIFIQUES:
        1. Identifie le message d'erreur principal et sa signification exacte
        2. Analyse le contexte temporel (horodatage, séquence d'événements)
        3. Détermine les causes racines possibles
        4. Propose des solutions techniques concrètes
        5. Suggère des mesures préventives

        IMPORTANT: Respecte EXACTEMENT le format de réponse structuré défini dans le prompt système avec les emojis et sections.
        """
        
        # Envoyer le message à ChatGPT
        response = self.send_message(
            message=analysis_prompt,
            system_prompt=agent_prompt,
            max_tokens=4000,
            temperature=0.1
        )

        # Formater la réponse pour s'assurer qu'elle suit la structure attendue
        formatted_content = self.formatter.format_structured_response(
            raw_response=response.content,
            error_content=error_content
        )

        # Créer une nouvelle réponse avec le contenu formaté
        response.content = formatted_content
        return response
    
    def get_code_suggestions(
        self,
        code_snippet: str,
        error_description: str
    ) -> ChatGPTResponse:
        """
        Obtient des suggestions pour corriger un bout de code
        
        Args:
            code_snippet: Extrait de code problématique
            error_description: Description de l'erreur
        
        Returns:
            Suggestions de correction
        """
        
        suggestion_prompt = f"""
        Voici un extrait de code qui pose problème:
        
        CODE:
        ```
        {code_snippet}
        ```
        
        ERREUR:
        {error_description}
        
        Fournis:
        1. Une version corrigée du code
        2. Une explication des modifications apportées
        3. Des bonnes pratiques pour éviter ce type d'erreur
        """
        
        return self.send_message(
            message=suggestion_prompt,
            system_prompt="Tu es un expert en programmation C/C++ et SQL embarqué. Fournis des solutions précises et pratiques.",
            max_tokens=2000,
            temperature=0.1
        )
    
    def validate_api_key(self) -> bool:
        """
        Valide la clé API
        
        Returns:
            True si la clé est valide
        """
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Test"}],
                max_tokens=10
            )
            return True
        except Exception:
            return False
