# Système Multi-Agents d'Analyse d'Erreurs

## 🎯 Objectif

Ce système multi-agents est conçu pour analyser automatiquement les fichiers de programme et leurs fichiers d'erreur associés. Il détecte les erreurs, localise leur emplacement et propose des solutions concrètes pour les corriger.

## 🏗️ Architecture

Le système suit une architecture modulaire avec les composants suivants :

```
agents_system/
├── agents/                 # Agents spécialisés
│   ├── base_agent.py      # Classe de base pour tous les agents
│   └── error_analyzer_agent.py  # Agent d'analyse d'erreurs
├── tools/                 # Outils spécialisés
│   ├── chatgpt_client.py  # Client ChatGPT 4.1
│   ├── code_analyzer.py   # Analyseur de code
│   └── error_analyzer.py  # Analyseur d'erreurs
├── utils/                 # Utilitaires
│   ├── file_handler.py    # Gestion des fichiers
│   ├── logger.py          # Système de logging
│   └── validators.py      # Validateurs
├── config/                # Configuration
│   └── config.py          # Configuration centrale
├── examples/              # Fichiers d'exemple
├── logs/                  # Fichiers de log
├── main.py               # Point d'entrée principal
├── demo.py               # Démonstration sans ChatGPT
└── requirements.txt      # Dépendances Python
```

## 🚀 Installation

1. **Cloner ou télécharger le projet**
2. **Installer les dépendances Python :**
   ```bash
   pip install -r agents_system/requirements.txt
   ```

## 🔧 Configuration

### Clé API ChatGPT 4.1

Modifiez le fichier `agents_system/config/config.py` pour configurer votre clé API OpenAI :

```python
OPENAI_API_KEY = "votre-clé-api-ici"
OPENAI_MODEL = "gpt-4-turbo-preview"  # ChatGPT 4.1
```

## 📖 Utilisation

### 1. Analyse complète avec ChatGPT

```bash
python agents_system/main.py
```

Cette commande :
- Analyse les fichiers d'exemple fournis
- Utilise les outils d'analyse locale
- Fait appel à ChatGPT 4.1 pour une analyse approfondie
- Génère un rapport complet

### 2. Démonstration sans ChatGPT

```bash
python agents_system/demo.py
```

Cette commande :
- Effectue une analyse complète avec les outils locaux
- Fournit des diagnostics détaillés
- Propose des solutions concrètes
- Ne nécessite pas de clé API

### 3. Test de la clé API

```bash
python agents_system/test_api.py
```

## 🔍 Fonctionnalités

### Analyse du Code
- **Détection d'erreurs** : Syntaxe, logique, sécurité
- **Analyse structurelle** : Fonctions, includes, commentaires
- **Support multi-langages** : C/C++, SQL embarqué
- **Classification par sévérité** : LOW, MEDIUM, HIGH, CRITICAL

### Analyse des Erreurs
- **Parsing des logs d'erreur** : Extraction automatique
- **Catégorisation** : Par type et sévérité
- **Timeline** : Chronologie des erreurs
- **Patterns reconnus** :
  - Erreurs de ressources (locked processes)
  - Erreurs de base de données (SQLCODE)
  - Erreurs de permissions
  - Erreurs réseau
  - Erreurs de mémoire

### Intégration ChatGPT 4.1
- **Analyse contextuelle** : Compréhension approfondie
- **Solutions personnalisées** : Adaptées au contexte
- **Explications détaillées** : Causes et corrections
- **Bonnes pratiques** : Recommandations préventives

## 📊 Exemple de Sortie

```
🔍 DÉMONSTRATION DU SYSTÈME D'AGENTS
==================================================

📁 Lecture des fichiers...
  ✅ Programme: 287456 caractères
  ✅ Erreurs: 156 caractères

🚨 ANALYSE DU FICHIER D'ERREUR
------------------------------
Total des erreurs détectées: 1
Répartition par sévérité:
  • HIGH: 1
Répartition par type:
  • RESOURCE_ERROR: 1

📋 DÉTAILS DES ERREURS
--------------------
1. Processus déjà en cours d'exécution
   Type: RESOURCE_ERROR
   Sévérité: HIGH
   Ligne 1: ERROR task [ORDER_LOOP] locked (already runs). EXIT.

🔍 ANALYSE DU CODE
------------------
Problèmes détectés dans le code: 45

📊 STRUCTURE DU CODE
--------------------
Lignes totales: 8623
Lignes de code: 6898
Lignes de commentaires: 1085
Lignes vides: 640
Fonctions détectées: 311
Includes détectés: 37

🎯 ANALYSE SPÉCIFIQUE DE L'ERREUR PRINCIPALE
---------------------------------------------
L'erreur principale détectée est:
  'ERROR task [ORDER_LOOP] locked (already runs). EXIT.'

🔍 DIAGNOSTIC:
  • Type: Erreur de ressource (RESOURCE_ERROR)
  • Cause: Un autre processus CAOFORS avec la tâche ORDER_LOOP est déjà en cours
  • Impact: Le programme ne peut pas démarrer

💡 SOLUTIONS RECOMMANDÉES:
  1. Vérifier les processus en cours: ps aux | grep caofors
  2. Arrêter le processus existant si nécessaire
  3. Vérifier les fichiers de verrouillage dans le répertoire temporaire
  4. Supprimer les fichiers .lock obsolètes si le processus n'existe plus

🛠️  COMMANDES SUGGÉRÉES:
  • Lister les processus: tasklist | findstr caofors
  • Arrêter un processus: taskkill /PID <pid> /F
  • Nettoyer les verrous: del /Q C:\temp\cao*\*.lock
```

## 🔧 Personnalisation

### Ajouter de nouveaux patterns d'erreur

Modifiez `tools/error_analyzer.py` :

```python
{
    'pattern': r'votre_pattern_regex',
    'type': ErrorType.VOTRE_TYPE,
    'severity': 'HIGH',
    'description': 'Description de l\'erreur'
}
```

### Créer un nouvel agent

1. Héritez de `BaseAgent`
2. Implémentez la méthode `execute()`
3. Ajoutez la configuration dans `config.py`

## 📝 Logs

Les logs sont automatiquement générés dans `agents_system/logs/` avec :
- Horodatage complet
- Niveaux de log (INFO, WARNING, ERROR)
- Traçabilité des opérations

## 🤝 Support

Le système est conçu pour être extensible et peut facilement être adapté à d'autres types de fichiers et d'erreurs.

## ✅ Statut

- ✅ Architecture multi-agents fonctionnelle
- ✅ Analyse locale complète
- ✅ Intégration ChatGPT 4.1 configurée
- ✅ Système de logging robuste
- ✅ Validation et gestion d'erreurs
- ✅ Documentation complète
