<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation Simple - LEONI AI Agents</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #002857 0%, #003366 100%);
            color: white;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            max-width: 600px;
        }
        
        h1 {
            color: #ff7514;
            margin-bottom: 2rem;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 1rem 2rem;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 117, 20, 0.4);
        }
        
        .url-info {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
        }
        
        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Test Navigation LEONI AI Agents</h1>
        
        <div class="url-info">
            <strong>URL Base:</strong> http://localhost:8500
        </div>
        
        <div class="buttons">
            <a href="http://localhost:8500" class="btn">🏠 Page d'Accueil</a>
            <a href="http://localhost:8500?page=analyzer" class="btn">🔍 Analyzer</a>
            <a href="http://localhost:8500?page=sql-generator" class="btn">🗄️ SQL Generator</a>
        </div>
        
        <div class="status">
            <h3>✅ Tests à effectuer :</h3>
            <ul style="text-align: left; margin-top: 1rem;">
                <li>Cliquer sur "Page d'Accueil" → Doit afficher l'Analyzer</li>
                <li>Cliquer sur "Analyzer" → Doit afficher l'interface d'analyse avec 2 colonnes</li>
                <li>Cliquer sur "SQL Generator" → Doit afficher l'interface SQL</li>
                <li>Utiliser la navbar sur chaque page pour naviguer</li>
                <li>Vérifier que l'état actif s'affiche correctement</li>
            </ul>
        </div>
        
        <div class="status">
            <h3>🔧 Fonctionnalités attendues :</h3>
            <ul style="text-align: left; margin-top: 1rem;">
                <li><strong>Analyzer:</strong> 2 colonnes (Fichier Programme + Fichier d'Erreur)</li>
                <li><strong>SQL Generator:</strong> Upload fichier + sélection base de données</li>
                <li><strong>Navigation:</strong> Navbar avec état actif</li>
                <li><strong>Design:</strong> Thème LEONI uniforme</li>
            </ul>
        </div>
    </div>
</body>
</html>
