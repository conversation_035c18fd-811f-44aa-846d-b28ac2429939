"""
Formateur de réponses structurées pour l'agent d'analyse d'erreurs
"""

import re
from typing import Dict, List, Any
from datetime import datetime

class ResponseFormatter:
    """Classe pour formater les réponses de l'agent selon un modèle structuré"""
    
    def __init__(self):
        """Initialise le formateur"""
        self.emoji_map = {
            'analysis': '🔍',
            'causes': '✅', 
            'solutions': '🛠️',
            'recommendations': '💡',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️',
            'success': '✅',
            'lock': '🔒',
            'process': '🔄',
            'database': '📊',
            'time': '⏰',
            'task': '📋',
            'exit': '🚪'
        }
    
    def format_structured_response(self, raw_response: str, error_content: str = "") -> str:
        """
        Formate une réponse brute en réponse structurée avec emojis
        
        Args:
            raw_response: Réponse brute de ChatGPT
            error_content: Contenu du fichier d'erreur pour extraction d'infos
            
        Returns:
            Réponse formatée avec structure et emojis
        """
        # Extraire les informations clés du message d'erreur
        error_info = self._extract_error_info(error_content)
        
        # Si la réponse est déjà bien formatée, la retourner
        if self._is_well_formatted(raw_response):
            return raw_response
        
        # Sinon, créer une structure basée sur le contenu
        return self._create_structured_response(raw_response, error_info)
    
    def _extract_error_info(self, error_content: str) -> Dict[str, Any]:
        """Extrait les informations clés du fichier d'erreur"""
        info = {
            'timestamp': None,
            'error_type': None,
            'task_name': None,
            'error_message': None,
            'severity': 'HIGH'
        }
        
        lines = error_content.strip().split('\n')
        
        for line in lines:
            # Extraction du timestamp
            timestamp_match = re.search(r'(\d{2}\.\d{2}\.\d{4}\s+\d{2}:\d{2}:\d{2})', line)
            if timestamp_match and not info['timestamp']:
                info['timestamp'] = timestamp_match.group(1)
            
            # Extraction du nom de tâche
            task_match = re.search(r'task\s+\[([^\]]+)\]', line)
            if task_match:
                info['task_name'] = task_match.group(1)
            
            # Extraction du type d'erreur
            if 'locked' in line.lower() and 'already runs' in line.lower():
                info['error_type'] = 'TASK_LOCK'
                info['error_message'] = 'Tâche déjà en cours d\'exécution'
            elif 'SQLCODE' in line:
                info['error_type'] = 'DATABASE_ERROR'
                info['error_message'] = 'Erreur de base de données'
            elif 'permission denied' in line.lower():
                info['error_type'] = 'PERMISSION_ERROR'
                info['error_message'] = 'Erreur de permissions'
        
        return info
    
    def _is_well_formatted(self, response: str) -> bool:
        """Vérifie si la réponse est déjà bien formatée"""
        required_sections = ['🔍 Analyse de l\'erreur', '✅ Causes possibles', '🛠️ Solutions possibles']
        return all(section in response for section in required_sections)
    
    def _create_structured_response(self, raw_response: str, error_info: Dict[str, Any]) -> str:
        """Crée une réponse structurée basée sur les informations extraites"""
        
        # En-tête avec le message d'erreur principal
        header = self._create_error_header(error_info)
        
        # Sections structurées
        analysis_section = self._create_analysis_section(error_info, raw_response)
        causes_section = self._create_causes_section(error_info, raw_response)
        solutions_section = self._create_solutions_section(error_info, raw_response)
        recommendations_section = self._create_recommendations_section(error_info, raw_response)
        
        return f"{header}\n\n{analysis_section}\n\n{causes_section}\n\n{solutions_section}\n\n{recommendations_section}"
    
    def _create_error_header(self, error_info: Dict[str, Any]) -> str:
        """Crée l'en-tête avec le message d'erreur principal"""
        if error_info['timestamp'] and error_info['task_name']:
            return f"Le message d'erreur du {error_info['timestamp']} concernant la tâche **{error_info['task_name']}** :"
        return "Le message d'erreur analysé :"
    
    def _create_analysis_section(self, error_info: Dict[str, Any], raw_response: str) -> str:
        """Crée la section d'analyse de l'erreur"""
        section = f"{self.emoji_map['analysis']} **Analyse de l'erreur :**\n\n"
        
        if error_info['error_type'] == 'TASK_LOCK':
            section += f"    • **task [{error_info['task_name']}] locked** : Cela indique que la tâche nommée {error_info['task_name']} est verrouillée.\n\n"
            section += f"    • **(already runs)** : Elle est déjà en cours d'exécution, donc une seconde instance ne peut pas démarrer.\n\n"
            section += f"    • **EXIT.** : Le processus a quitté sans exécuter la tâche à nouveau pour éviter un conflit.\n"
        else:
            # Extraire les points d'analyse du raw_response
            section += self._extract_analysis_points(raw_response)
        
        return section
    
    def _create_causes_section(self, error_info: Dict[str, Any], raw_response: str) -> str:
        """Crée la section des causes possibles"""
        section = f"{self.emoji_map['causes']} **Causes possibles :**\n\n"
        
        if error_info['error_type'] == 'TASK_LOCK':
            section += f"    • **Exécution en double** : Un précédent lancement de {error_info['task_name']} n'est pas encore terminé.\n\n"
            section += f"    • **Blocage ou crash** : La tâche précédente est peut-être bloquée ou a planté sans libérer le verrou.\n\n"
            section += f"    • **Pas de gestion de timeout** : Le verrou est toujours considéré actif même si le processus est mort ou bloqué.\n\n"
            section += f"    • **Défaut de synchronisation dans l'application** : Mauvaise gestion de tâches concurrentes.\n"
        else:
            section += self._extract_causes_points(raw_response)
        
        return section
    
    def _create_solutions_section(self, error_info: Dict[str, Any], raw_response: str) -> str:
        """Crée la section des solutions possibles"""
        section = f"{self.emoji_map['solutions']} **Solutions possibles :**\n\n"
        
        if error_info['error_type'] == 'TASK_LOCK':
            section += f"    • **Vérifier si {error_info['task_name']} tourne encore** : Peut-être via un outil de monitoring ou un processus actif.\n\n"
            section += f"    • **Libérer manuellement le verrou** : Si possible, selon le système, vider le fichier de lock ou réinitialiser l'état.\n\n"
            section += f"    • **Ajouter une vérification de durée** : Mettre en place un timeout ou une vérification pour forcer l'arrêt du verrou après X minutes.\n\n"
            section += f"    • **Revoir la logique de lancement** : Empêcher le lancement simultané si la tâche tourne encore sans avoir besoin de forcer une erreur.\n"
        else:
            section += self._extract_solutions_points(raw_response)
        
        return section
    
    def _create_recommendations_section(self, error_info: Dict[str, Any], raw_response: str) -> str:
        """Crée la section des recommandations préventives"""
        section = f"{self.emoji_map['recommendations']} **Recommandations préventives :**\n\n"
        
        if error_info['error_type'] == 'TASK_LOCK':
            section += f"    • **Implémenter un système de monitoring** : Surveiller l'état des tâches en temps réel.\n\n"
            section += f"    • **Ajouter des timeouts automatiques** : Libérer automatiquement les verrous après une durée définie.\n\n"
            section += f"    • **Améliorer la gestion des erreurs** : Logger les états de début/fin de tâche pour faciliter le debug.\n"
        else:
            section += self._extract_recommendations_points(raw_response)
        
        return section
    
    def _extract_analysis_points(self, text: str) -> str:
        """Extrait les points d'analyse du texte brut"""
        # Logique d'extraction basique - peut être améliorée
        return "    • **Analyse en cours** : Détails de l'erreur identifiée.\n"
    
    def _extract_causes_points(self, text: str) -> str:
        """Extrait les causes du texte brut"""
        return "    • **Cause identifiée** : Analyse des causes possibles.\n"
    
    def _extract_solutions_points(self, text: str) -> str:
        """Extrait les solutions du texte brut"""
        return "    • **Solution proposée** : Actions correctives suggérées.\n"
    
    def _extract_recommendations_points(self, text: str) -> str:
        """Extrait les recommandations du texte brut"""
        return "    • **Recommandation** : Bonnes pratiques à adopter.\n"
