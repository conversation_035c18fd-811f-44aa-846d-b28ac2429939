#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la nouvelle clé API avec gestion d'encodage UTF-8
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.chatgpt_client import ChatGPTClient

def test_new_api_key():
    """Test la nouvelle clé API avec gestion UTF-8"""
    print("🔑 Test de la nouvelle clé API OpenAI...")
    
    # Nouvelle clé API corrigée (caractères ASCII uniquement)
    api_key = "sk-proj-pUkzo4FarIw0n_-w1T0Uq6DR32i0sr--LQnkNG8QDL0ZI97wdYP7p6Ca8tq6f1MLW5QXK7bWx9T3BLbkFJ-xhLBLQP8egicmVRslfJ1Ghw21eAKTBGBherW0V-6AZiBGyQSS0HDoNJ-j93TmE8d_FHNrYA"
    
    print(f"Clé API: {api_key[:15]}...")
    print(f"Longueur de la clé: {len(api_key)} caractères")
    
    try:
        print("\n🤖 Initialisation du client ChatGPT...")
        client = ChatGPTClient(
            api_key=api_key,
            model="gpt-3.5-turbo"  # Utilisons un modèle plus simple pour le test
        )
        
        print("✅ Client initialisé avec succès")
        
        print("\n📝 Test d'analyse simple...")
        response = client.analyze_code_errors(
            program_content="int main() { return 0; }",
            error_content="Test de connexion avec nouvelle clé",
            agent_prompt="Analysez ce code simple et confirmez que la connexion fonctionne."
        )
        
        if response and hasattr(response, 'content') and response.content:
            print("✅ SUCCESS: La nouvelle clé API fonctionne parfaitement !")
            print(f"📝 Réponse reçue: {len(response.content)} caractères")
            print(f"🎯 Aperçu: {response.content[:100]}...")
            return True
        else:
            print("❌ ERREUR: Réponse vide ou invalide")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR: {str(e)}")
        print(f"Type d'erreur: {type(e).__name__}")
        return False

if __name__ == "__main__":
    success = test_new_api_key()
    if success:
        print("\n🎉 La nouvelle clé API est valide et fonctionnelle !")
    else:
        print("\n💥 La nouvelle clé API ne fonctionne pas correctement.")
