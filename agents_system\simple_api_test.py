#!/usr/bin/env python3
"""
Test simple de la clé API OpenAI
"""

import requests
import json
import urllib3

# Désactiver les avertissements SSL pour le test
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_openai_api():
    """Test simple avec requests"""
    api_key = "sk-pгoj-pUkzо4FarIw0n_-w1T0Uq6DR32і0sг--LQnkNG8QDL0ZI97wdYP7p6Ca8tq6f1MLW5QXK7bWx9TЗBLbkFJ—xhLßLQP8egicmVRslfJ1Ghw21eAKTBGBherW0V-6AZіBGyQSS0HDoNJ-j9ЗTmE8d_FHNгYA"
    
    print("🔑 Test de la clé API OpenAI avec requests...")
    print(f"Clé API: {api_key[:20]}...")
    
    # Test de connectivité internet
    try:
        print("\n🌐 Test de connectivité internet...")
        response = requests.get("https://httpbin.org/ip", timeout=10, verify=False)
        if response.status_code == 200:
            print("✅ Connexion internet OK")
        else:
            print("❌ Problème de connexion internet")
            return False
    except Exception as e:
        print(f"❌ Pas de connexion internet: {e}")
        print("🔄 Tentative directe avec l'API OpenAI...")
    
    # Test de l'API OpenAI
    try:
        print("\n🤖 Test de l'API OpenAI...")
        
        # S'assurer que la clé API est correctement encodée
        api_key_encoded = api_key.encode('utf-8').decode('utf-8')

        headers = {
            "Authorization": f"Bearer {api_key_encoded}",
            "Content-Type": "application/json; charset=utf-8"
        }
        
        data = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "user", "content": "Réponds simplement 'API fonctionnelle' si tu reçois ce message."}
            ],
            "max_tokens": 50
        }
        
        response = requests.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30,
            verify=False
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Text: {response.text[:200]}...")

        if response.status_code == 200:
            try:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"✅ SUCCESS: La clé API fonctionne !")
                print(f"📝 Réponse: {content}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ ERREUR JSON: {e}")
                print(f"Response complet: {response.text}")
                return False
        else:
            print(f"❌ ERREUR: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_openai_api()
    if success:
        print("\n🎉 La clé API est valide et fonctionnelle !")
    else:
        print("\n💥 La clé API ne fonctionne pas.")
