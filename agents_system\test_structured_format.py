"""
Script de test pour vérifier le nouveau format structuré des réponses
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.response_formatter import ResponseFormatter
from tools.chatgpt_client import ChatGPTClient
from config.config import DEFAULT_CONFIG

def test_response_formatter():
    """Test du formateur de réponse"""
    print("🧪 Test du formateur de réponse...")
    
    formatter = ResponseFormatter()
    
    # Exemple de fichier d'erreur CAOFORS
    error_content = """16.07.2025 12:00:52 CAOFORS ERROR task [ORDER_LOOP] locked (already runs ). EXIT.
16.07.2025 12:00:52 CAOFORS ERROR end"""
    
    # Réponse brute simulée
    raw_response = """Cette erreur indique que la tâche ORDER_LOOP est déjà en cours d'exécution. 
    Il s'agit d'un problème de verrou qui empêche le lancement d'une seconde instance."""
    
    # Formater la réponse
    formatted_response = formatter.format_structured_response(raw_response, error_content)
    
    print("📄 Réponse formatée :")
    print("-" * 50)
    print(formatted_response)
    print("-" * 50)
    
    return formatted_response

def test_chatgpt_integration():
    """Test de l'intégration avec ChatGPT (nécessite une clé API valide)"""
    print("\n🤖 Test de l'intégration ChatGPT...")
    
    try:
        # Vérifier si la clé API est configurée
        if not DEFAULT_CONFIG.OPENAI_API_KEY or DEFAULT_CONFIG.OPENAI_API_KEY == "your-api-key-here":
            print("⚠️ Clé API non configurée - test ignoré")
            return None
        
        client = ChatGPTClient(DEFAULT_CONFIG.OPENAI_API_KEY, DEFAULT_CONFIG.OPENAI_MODEL)
        
        # Exemple de contenu de programme
        program_content = """
        #include <stdio.h>
        #include <sqlca.h>
        
        int main() {
            EXEC SQL CONNECT TO database;
            if (SQLCODE < 0) {
                printf("Erreur de connexion\\n");
                return -1;
            }
            return 0;
        }
        """
        
        # Exemple de fichier d'erreur
        error_content = """16.07.2025 12:00:52 CAOFORS ERROR task [ORDER_LOOP] locked (already runs ). EXIT.
16.07.2025 12:00:52 CAOFORS ERROR end"""
        
        # Obtenir le prompt de l'agent
        agent_prompt = DEFAULT_CONFIG.get_agent_config('error_analyzer')['prompt']
        
        # Analyser avec ChatGPT
        response = client.analyze_code_errors(
            program_content=program_content,
            error_content=error_content,
            agent_prompt=agent_prompt
        )
        
        print("📄 Réponse ChatGPT formatée :")
        print("-" * 50)
        print(response.content)
        print("-" * 50)
        
        return response.content
        
    except Exception as e:
        print(f"❌ Erreur lors du test ChatGPT : {str(e)}")
        return None

def validate_format(response_text):
    """Valide que la réponse suit le format attendu"""
    print("\n✅ Validation du format...")
    
    required_sections = [
        '🔍',  # Analyse de l'erreur
        '✅',  # Causes possibles  
        '🛠️',  # Solutions possibles
        '💡'   # Recommandations préventives
    ]
    
    validation_results = {}
    
    for emoji in required_sections:
        if emoji in response_text:
            validation_results[emoji] = "✅ Présent"
        else:
            validation_results[emoji] = "❌ Manquant"
    
    print("📊 Résultats de validation :")
    for emoji, status in validation_results.items():
        section_name = {
            '🔍': 'Analyse de l\'erreur',
            '✅': 'Causes possibles',
            '🛠️': 'Solutions possibles', 
            '💡': 'Recommandations préventives'
        }.get(emoji, emoji)
        print(f"  {section_name}: {status}")
    
    all_present = all("✅" in status for status in validation_results.values())
    print(f"\n🎯 Format global: {'✅ Valide' if all_present else '❌ Invalide'}")
    
    return all_present

def main():
    """Fonction principale de test"""
    print("🚀 Test du nouveau format structuré")
    print("=" * 50)
    
    # Test 1: Formateur seul
    formatted_response = test_response_formatter()
    
    # Validation du format
    is_valid = validate_format(formatted_response)
    
    # Test 2: Intégration ChatGPT (optionnel)
    chatgpt_response = test_chatgpt_integration()
    if chatgpt_response:
        print("\n🔍 Validation de la réponse ChatGPT...")
        validate_format(chatgpt_response)
    
    print("\n🏁 Tests terminés!")
    
    if is_valid:
        print("✅ Le formateur fonctionne correctement!")
    else:
        print("❌ Des améliorations sont nécessaires.")

if __name__ == "__main__":
    main()
