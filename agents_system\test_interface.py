#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface de test simplifiée pour diagnostiquer les problèmes
"""

import streamlit as st

# Configuration de la page
st.set_page_config(
    page_title="Test Interface LEONI",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# CSS simplifié
st.markdown("""
<style>
    .stApp {
        background: linear-gradient(135deg, #002857 0%, #003366 100%);
    }
    
    .main-title {
        color: white;
        text-align: center;
        font-size: 2.5rem;
        margin: 2rem 0;
    }
    
    .test-box {
        background: rgba(255, 255, 255, 0.95);
        padding: 2rem;
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow: 0 8px 32px rgba(0, 40, 87, 0.2);
    }
    
    .stButton > button {
        background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 25px !important;
        padding: 0.75rem 2rem !important;
        font-weight: 600 !important;
    }
</style>
""", unsafe_allow_html=True)

# Contenu de test
st.markdown('<h1 class="main-title">🤖 Test Interface LEONI</h1>', unsafe_allow_html=True)

st.markdown("""
<div class="test-box">
    <h2>✅ Interface de test fonctionnelle</h2>
    <p>Si vous voyez cette page avec les couleurs LEONI (bleu et orange), l'interface fonctionne correctement.</p>
    <ul>
        <li><strong>Arrière-plan :</strong> Dégradé bleu LEONI</li>
        <li><strong>Conteneur :</strong> Fond blanc avec ombres</li>
        <li><strong>Boutons :</strong> Orange LEONI</li>
    </ul>
</div>
""", unsafe_allow_html=True)

col1, col2, col3 = st.columns(3)

with col1:
    st.metric("Test Métrique 1", "100%", "✅")

with col2:
    st.metric("Test Métrique 2", "OK", "🎯")

with col3:
    st.metric("Test Métrique 3", "Actif", "🚀")

if st.button("🧪 Bouton de Test"):
    st.success("✅ Le bouton fonctionne parfaitement !")
    st.balloons()

st.markdown("""
<div class="test-box">
    <h3>🔧 Diagnostic</h3>
    <p>Cette interface de test permet de vérifier :</p>
    <ol>
        <li>Le chargement du CSS</li>
        <li>Les couleurs LEONI</li>
        <li>Les composants Streamlit</li>
        <li>Les interactions</li>
    </ol>
</div>
""", unsafe_allow_html=True)

# Upload de test
uploaded_file = st.file_uploader("📁 Test d'upload de fichier", type=['txt', 'py', 'c', 'cpp'])

if uploaded_file:
    st.success(f"✅ Fichier uploadé : {uploaded_file.name}")
    
# Sidebar de test
with st.sidebar:
    st.header("🔧 Test Sidebar")
    st.info("Sidebar fonctionnelle")
    test_slider = st.slider("Test Slider", 0, 100, 50)
    st.write(f"Valeur : {test_slider}")
