#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application Simple - LEONI AI Agent d'Analyse d'Erreurs
Version simplifiée avec un seul agent
"""

import streamlit as st
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(str(Path(__file__).parent))

# Configuration de la page
st.set_page_config(
    page_title="LEONI AI - Analyse d'Erreurs",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# CSS Premium LEONI Simplifié
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
    
    /* Masquer les éléments Streamlit */
    .stDeployButton, #MainMenu, header[data-testid="stHeader"], 
    footer, [data-testid="stToolbar"] {
        display: none !important;
        visibility: hidden !important;
    }

    /* Arrière-plan LEONI */
    .stApp {
        background: linear-gradient(135deg, #002857 0%, #003366 50%, #004080 100%);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        position: relative;
        min-height: 100vh;
    }
    
    .stApp::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: 
            radial-gradient(circle at 20% 20%, rgba(255, 117, 20, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(0, 40, 87, 0.15) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }

    /* Header Premium */
    .main-header {
        font-size: 3rem;
        font-weight: 800;
        color: white;
        text-align: center;
        margin: 2rem 0;
        background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        letter-spacing: -0.03em;
    }
    
    .subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        margin-bottom: 3rem;
        font-weight: 400;
    }

    /* Conteneurs avec Glassmorphism */
    .stContainer > div, .element-container {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border-radius: 20px !important;
        padding: 2rem !important;
        margin: 1.5rem 0 !important;
        box-shadow: 0 8px 32px rgba(0, 40, 87, 0.15) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        transition: all 0.3s ease !important;
    }
    
    .stContainer > div:hover, .element-container:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 12px 40px rgba(0, 40, 87, 0.2) !important;
    }

    /* Boutons Premium */
    .stButton > button {
        background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 50px !important;
        padding: 1rem 2.5rem !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        font-size: 1.1rem !important;
        box-shadow: 0 8px 25px rgba(255, 117, 20, 0.3) !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
    }
    
    .stButton > button:hover {
        background: linear-gradient(135deg, #ff8f44 0%, #ff7514 100%) !important;
        transform: translateY(-2px) scale(1.02) !important;
        box-shadow: 0 12px 35px rgba(255, 117, 20, 0.4) !important;
    }

    /* Typographie */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        color: #002857 !important;
    }
    
    p, div, span {
        font-family: 'Inter', sans-serif !important;
        line-height: 1.6 !important;
        color: rgba(0, 40, 87, 0.8) !important;
    }

    /* File uploader styling */
    .stFileUploader > div {
        border: 2px dashed rgba(255, 117, 20, 0.3) !important;
        border-radius: 15px !important;
        padding: 2rem !important;
        background: rgba(255, 117, 20, 0.05) !important;
    }
    
    .stFileUploader:hover > div {
        border-color: #ff7514 !important;
        background: rgba(255, 117, 20, 0.1) !important;
    }

    /* Success/Error messages */
    .stSuccess {
        background: rgba(34, 197, 94, 0.1) !important;
        border: 1px solid rgba(34, 197, 94, 0.3) !important;
        border-radius: 15px !important;
    }
    
    .stError {
        background: rgba(239, 68, 68, 0.1) !important;
        border: 1px solid rgba(239, 68, 68, 0.3) !important;
        border-radius: 15px !important;
    }
    
    .stInfo {
        background: rgba(59, 130, 246, 0.1) !important;
        border: 1px solid rgba(59, 130, 246, 0.3) !important;
        border-radius: 15px !important;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .main-header {
            font-size: 2rem;
        }
        
        .stContainer > div, .element-container {
            padding: 1rem !important;
            margin: 1rem 0 !important;
        }
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session"""
    if 'code_analyzer' not in st.session_state:
        from tools.code_analyzer import CodeAnalyzer
        st.session_state.code_analyzer = CodeAnalyzer()
    
    if 'error_analyzer' not in st.session_state:
        from tools.error_analyzer import ErrorAnalyzer
        st.session_state.error_analyzer = ErrorAnalyzer()

def detect_file_language(filename):
    """Détecte le langage d'un fichier basé sur son extension"""
    extension = filename.lower().split('.')[-1] if '.' in filename else ''
    
    language_map = {
        'py': 'python', 'js': 'javascript', 'ts': 'typescript',
        'java': 'java', 'c': 'c', 'cpp': 'cpp', 'cc': 'cpp',
        'h': 'c', 'hpp': 'cpp', 'cs': 'csharp', 'php': 'php',
        'rb': 'ruby', 'go': 'go', 'rs': 'rust', 'swift': 'swift',
        'kt': 'kotlin', 'scala': 'scala', 'r': 'r', 'sql': 'sql',
        'html': 'html', 'css': 'css', 'xml': 'xml', 'json': 'json',
        'yaml': 'yaml', 'yml': 'yaml', 'sh': 'bash', 'bat': 'batch',
        'ps1': 'powershell', 'md': 'markdown', 'txt': 'text',
        'log': 'text', 'error': 'text'
    }
    
    return language_map.get(extension, 'text')

def display_header():
    """Affiche l'en-tête de l'application"""
    st.markdown("""
    <div class="main-header">
        🔍 LEONI AI - Analyse d'Erreurs
    </div>
    <div class="subtitle">
        Agent intelligent pour l'analyse de code et la détection d'erreurs
    </div>
    """, unsafe_allow_html=True)

def display_file_upload():
    """Section de téléchargement de fichiers"""
    st.header("📁 Téléchargement des Fichiers")
    st.info("💡 **Tous types de fichiers sont acceptés** - L'interface détectera automatiquement le format")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📄 Fichier Programme")
        program_file = st.file_uploader(
            "Choisissez un fichier programme",
            help="Tous types de fichiers acceptés (code source, scripts, etc.)",
            key="program_file"
        )

        if program_file:
            st.success(f"✅ Fichier chargé: {program_file.name}")
            file_size = len(program_file.getvalue())
            st.info(f"📊 Taille: {file_size:,} octets")

            # Aperçu du fichier
            with st.expander("👁️ Aperçu du fichier"):
                try:
                    content = program_file.read().decode('utf-8')
                    language = detect_file_language(program_file.name)
                    st.code(content[:500] + "..." if len(content) > 500 else content, language=language)
                    program_file.seek(0)  # Reset file pointer
                except UnicodeDecodeError:
                    try:
                        program_file.seek(0)
                        content = program_file.read().decode('latin-1')
                        language = detect_file_language(program_file.name)
                        st.code(content[:500] + "..." if len(content) > 500 else content, language=language)
                        program_file.seek(0)
                    except:
                        st.warning("⚠️ Fichier binaire ou encodage non supporté pour l'aperçu")
                        st.info("💡 Le fichier sera tout de même analysé")

    with col2:
        st.subheader("🚨 Fichier d'Erreur")
        error_file = st.file_uploader(
            "Choisissez un fichier d'erreur",
            help="Tous types de fichiers acceptés (logs, erreurs, traces, etc.)",
            key="error_file"
        )

        if error_file:
            st.success(f"✅ Fichier chargé: {error_file.name}")
            file_size = len(error_file.getvalue())
            st.info(f"📊 Taille: {file_size:,} octets")

            # Aperçu du fichier
            with st.expander("👁️ Aperçu du fichier"):
                try:
                    content = error_file.read().decode('utf-8')
                    st.code(content[:500] + "..." if len(content) > 500 else content, language="text")
                    error_file.seek(0)  # Reset file pointer
                except UnicodeDecodeError:
                    try:
                        error_file.seek(0)
                        content = error_file.read().decode('latin-1')
                        st.code(content[:500] + "..." if len(content) > 500 else content, language="text")
                        error_file.seek(0)
                    except:
                        st.warning("⚠️ Fichier binaire ou encodage non supporté pour l'aperçu")
                        st.info("💡 Le fichier sera tout de même analysé")

    return program_file, error_file

def analyze_files(program_file, error_file):
    """Analyse les fichiers uploadés"""
    try:
        # Lire le contenu des fichiers
        try:
            program_content = program_file.getvalue().decode('utf-8')
        except UnicodeDecodeError:
            program_content = program_file.getvalue().decode('latin-1')

        try:
            error_content = error_file.getvalue().decode('utf-8')
        except UnicodeDecodeError:
            error_content = error_file.getvalue().decode('latin-1')

        program_name = program_file.name
        error_name = error_file.name
        
        # Analyse locale
        with st.spinner("🔍 Analyse en cours..."):
            # Analyser le code
            code_errors = st.session_state.code_analyzer.analyze_code(program_content, "c")
            code_structure = st.session_state.code_analyzer.analyze_structure(program_content)
            
            # Analyser les erreurs
            error_entries = st.session_state.error_analyzer.analyze_error_file(error_content)
            error_summary = st.session_state.error_analyzer.get_error_summary(error_entries)
            error_categories = st.session_state.error_analyzer.categorize_errors(error_entries)
        
        # Préparer les résultats
        results = {
            'program_name': program_name,
            'error_name': error_name,
            'program_content': program_content,
            'error_content': error_content,
            'code_errors': code_errors,
            'code_structure': code_structure,
            'error_entries': error_entries,
            'error_summary': error_summary,
            'error_categories': error_categories
        }
        
        return results
        
    except Exception as e:
        st.error(f"❌ Erreur lors de l'analyse: {str(e)}")
        return None

def display_results(results):
    """Affiche les résultats d'analyse"""
    if not results:
        return

    st.header("📊 Résultats d'Analyse")
    
    # Métriques principales
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Erreurs Détectées", 
            len(results['error_entries']),
            help="Nombre total d'erreurs trouvées dans le fichier d'erreur"
        )
    
    with col2:
        st.metric(
            "Problèmes Code", 
            len(results['code_errors']),
            help="Nombre de problèmes détectés dans le code source"
        )
    
    with col3:
        st.metric(
            "Lignes de Code", 
            results['code_structure']['code_lines'],
            help="Nombre de lignes de code (hors commentaires et lignes vides)"
        )
    
    with col4:
        st.metric(
            "Fonctions", 
            len(results['code_structure']['functions']),
            help="Nombre de fonctions détectées dans le code"
        )

    # Détails des erreurs
    if results['error_entries']:
        st.subheader("🚨 Erreurs du Fichier Log")
        
        # Résumé par sévérité
        if results['error_summary']['by_severity']:
            st.write("**Répartition par sévérité:**")
            severity_data = results['error_summary']['by_severity']
            for severity, count in severity_data.items():
                st.write(f"• {severity}: {count}")
        
        # Liste des erreurs (limitée aux 10 premières)
        st.write("**Détails des erreurs (10 premières):**")
        for i, error in enumerate(results['error_entries'][:10], 1):
            with st.expander(f"Erreur {i}: {error.message[:80]}..."):
                st.write(f"**Type:** {error.error_type.value}")
                st.write(f"**Sévérité:** {error.severity}")
                st.write(f"**Ligne:** {error.line_number}")
                st.code(error.raw_line, language="text")
                if error.timestamp:
                    st.write(f"**Timestamp:** {error.timestamp}")

    # Problèmes du code
    if results['code_errors']:
        st.subheader("⚠️ Problèmes du Code")
        
        for i, error in enumerate(results['code_errors'][:10], 1):
            with st.expander(f"Problème {i}: {error.message}"):
                st.write(f"**Type:** {error.error_type}")
                st.write(f"**Sévérité:** {error.severity.value}")
                st.write(f"**Ligne:** {error.line_number}")
                st.code(error.code_snippet, language="c")
                if error.suggestion:
                    st.info(f"💡 **Suggestion:** {error.suggestion}")

    # Recommandations
    st.subheader("💡 Recommandations")
    
    if results['error_entries']:
        main_error = results['error_entries'][0]
        if "locked" in main_error.message.lower():
            st.warning("🔒 **Problème de verrouillage détecté**")
            st.write("**Solutions recommandées:**")
            st.write("1. Vérifier les processus en cours: `tasklist | findstr caofors`")
            st.write("2. Arrêter le processus existant si nécessaire")
            st.write("3. Supprimer les fichiers de verrouillage obsolètes")
            st.write("4. Redémarrer le service si nécessaire")

def main():
    """Fonction principale"""
    # Initialisation
    init_session_state()
    
    # Interface
    display_header()
    
    # Upload de fichiers
    program_file, error_file = display_file_upload()
    
    # Analyse
    if program_file and error_file:
        if st.button("🚀 Analyser les fichiers", type="primary"):
            results = analyze_files(program_file, error_file)
            if results:
                st.session_state.analysis_results = results
                st.success("✅ Analyse terminée avec succès !")
    
    # Affichage des résultats
    if hasattr(st.session_state, 'analysis_results') and st.session_state.analysis_results:
        st.markdown("---")
        display_results(st.session_state.analysis_results)

if __name__ == "__main__":
    main()
