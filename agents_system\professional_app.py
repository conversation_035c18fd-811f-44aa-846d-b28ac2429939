#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LEONI AI Agent - Version Professionnelle
Interface épurée et moderne pour environnement corporate
"""

import streamlit as st
import sys
import os
from pathlib import Path
import time

# Ajouter le répertoire parent au path
sys.path.append(str(Path(__file__).parent))

# Configuration de la page
st.set_page_config(
    page_title="LEONI AI Agent",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# CSS Moderne et Élégant
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    /* Reset et base */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    /* Masquer éléments Streamlit */
    .stDeployButton, #MainMenu, header[data-testid="stHeader"],
    footer, [data-testid="stToolbar"], .stDecoration {
        display: none !important;
    }

    /* Application principale blanche avec accents LEONI */
    .stApp {
        background: #ffffff;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        color: #1a202c;
        line-height: 1.6;
        min-height: 100vh;
        position: relative;
    }

    .stApp::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(0, 40, 87, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 117, 20, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(0, 40, 87, 0.02) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
    }
    
    /* Container principal */
    .main .block-container {
        padding: 2rem 3rem;
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }

    /* Header LEONI avec design épuré */
    .app-header {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 16px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0, 40, 87, 0.08);
        position: relative;
        overflow: hidden;
    }

    .app-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #002857 0%, #ff7514 100%);
    }

    .app-title {
        font-size: 2.5rem;
        font-weight: 800;
        color: #002857;
        margin: 0;
        letter-spacing: -0.02em;
        text-align: center;
    }

    .app-subtitle {
        font-size: 1.1rem;
        color: #64748b;
        margin-top: 0.75rem;
        font-weight: 400;
        text-align: center;
    }

    /* Sections blanches avec accents LEONI */
    .section {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 40, 87, 0.06);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #002857 0%, #ff7514 100%);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .section:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 40, 87, 0.12);
        border-color: rgba(0, 40, 87, 0.1);
    }

    .section:hover::before {
        transform: scaleX(1);
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #002857;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        position: relative;
    }

    .section-title::after {
        content: '';
        flex: 1;
        height: 1px;
        background: linear-gradient(90deg, rgba(0, 40, 87, 0.2) 0%, transparent 100%);
        margin-left: 1rem;
    }

    /* Upload zones LEONI */
    .upload-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .upload-box {
        border: 2px dashed rgba(0, 40, 87, 0.2);
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        background: #fafbfc;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .upload-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(0, 40, 87, 0.02) 0%, rgba(255, 117, 20, 0.02) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .upload-box:hover {
        border-color: #002857;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 40, 87, 0.1);
        background: white;
    }

    .upload-box:hover::before {
        opacity: 1;
    }

    .upload-label {
        font-size: 1rem;
        font-weight: 600;
        color: #002857;
        margin-bottom: 0.75rem;
        position: relative;
        z-index: 1;
    }

    /* Boutons LEONI orange */
    .stButton > button {
        background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 10px !important;
        padding: 1rem 2rem !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 600 !important;
        font-size: 1rem !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 15px rgba(255, 117, 20, 0.3) !important;
        width: auto !important;
        position: relative;
        overflow: hidden;
    }

    .stButton > button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .stButton > button:hover {
        background: linear-gradient(135deg, #e6660a 0%, #ff7514 100%) !important;
        transform: translateY(-2px) scale(1.02) !important;
        box-shadow: 0 8px 25px rgba(255, 117, 20, 0.5) !important;
    }

    .stButton > button:hover::before {
        left: 100%;
    }

    /* Messages de statut LEONI */
    .status-message {
        padding: 1rem 1.5rem;
        border-radius: 8px;
        font-size: 0.95rem;
        font-weight: 500;
        margin: 1rem 0;
        border: 1px solid #e2e8f0;
        position: relative;
        overflow: hidden;
        background: white;
    }

    .status-message::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: currentColor;
    }

    .status-success {
        background: #f0fdf4;
        color: #166534;
        border-color: #bbf7d0;
    }

    .status-success::before {
        background: #22c55e;
    }

    .status-info {
        background: #eff6ff;
        color: #002857;
        border-color: rgba(0, 40, 87, 0.2);
    }

    .status-info::before {
        background: #002857;
    }

    /* Métriques LEONI */
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .metric-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 40, 87, 0.04);
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #002857 0%, #ff7514 100%);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 40, 87, 0.12);
        border-color: rgba(0, 40, 87, 0.1);
    }

    .metric-card:hover::before {
        transform: scaleX(1);
    }

    .metric-value {
        font-size: 2.25rem;
        font-weight: 800;
        color: #002857;
        margin-bottom: 0.5rem;
    }

    .metric-label {
        font-size: 0.85rem;
        color: #64748b;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.1em;
    }

    /* Résultats */
    .result-item {
        border-left: 3px solid #e2e8f0;
        padding-left: 1rem;
        margin: 1rem 0;
    }
    
    .result-title {
        font-size: 0.95rem;
        font-weight: 600;
        color: #1a202c;
        margin-bottom: 0.5rem;
    }
    
    .result-content {
        font-size: 0.9rem;
        color: #475569;
        line-height: 1.5;
    }

    /* Code blocks */
    .stCode {
        background: #f8fafc !important;
        border: 1px solid #e2e8f0 !important;
        border-radius: 6px !important;
        font-size: 0.85rem !important;
    }

    /* Markdown styling LEONI */
    .stMarkdown h3 {
        color: #002857 !important;
        font-size: 1.1rem !important;
        font-weight: 600 !important;
        margin: 1.5rem 0 0.75rem 0 !important;
    }

    .stMarkdown p {
        color: #475569 !important;
        font-size: 0.95rem !important;
        line-height: 1.6 !important;
        margin-bottom: 1rem !important;
    }

    .stMarkdown ul, .stMarkdown ol {
        color: #475569 !important;
        font-size: 0.95rem !important;
        line-height: 1.6 !important;
        margin-left: 1rem !important;
    }

    .stMarkdown li {
        margin-bottom: 0.5rem !important;
    }

    .stMarkdown strong {
        color: #002857 !important;
        font-weight: 600 !important;
    }

    .stMarkdown code {
        background: #f8fafc !important;
        color: #002857 !important;
        padding: 0.2rem 0.4rem !important;
        border-radius: 4px !important;
        font-size: 0.85rem !important;
        border: 1px solid #e2e8f0 !important;
    }

    /* Progress LEONI */
    .progress-container {
        background: #f1f5f9;
        border-radius: 6px;
        height: 8px;
        margin: 1rem 0;
        overflow: hidden;
        border: 1px solid #e2e8f0;
    }

    .progress-bar {
        background: linear-gradient(90deg, #002857 0%, #ff7514 100%);
        height: 100%;
        border-radius: 6px;
        transition: width 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 40, 87, 0.2);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .main .block-container {
            padding: 1rem;
        }
        
        .upload-container {
            grid-template-columns: 1fr;
        }
        
        .metrics-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    /* Animations et effets */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    .slide-up {
        animation: slideUp 0.8s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Effet subtil pour le header */
    .app-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(0, 40, 87, 0.02) 2px, transparent 2px),
            radial-gradient(circle at 75% 75%, rgba(255, 117, 20, 0.01) 1px, transparent 1px);
        background-size: 60px 60px, 40px 40px;
        pointer-events: none;
    }

    /* Effet de brillance sur les sections */
    .section::after {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        transform: rotate(45deg);
        transition: all 0.6s ease;
        opacity: 0;
    }

    .section:hover::after {
        animation: shine 0.6s ease-out;
    }

    @keyframes shine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
        50% { opacity: 1; }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise les variables de session"""
    if 'code_analyzer' not in st.session_state:
        from tools.code_analyzer import CodeAnalyzer
        st.session_state.code_analyzer = CodeAnalyzer()
    
    if 'error_analyzer' not in st.session_state:
        from tools.error_analyzer import ErrorAnalyzer
        st.session_state.error_analyzer = ErrorAnalyzer()

def display_header():
    """Header moderne avec effets"""
    st.markdown("""
    <div class="app-header fade-in">
        <div class="app-title">🤖 LEONI AI Agent</div>
        <div class="app-subtitle">Analyse automatique d'erreurs et diagnostic intelligent</div>
    </div>
    """, unsafe_allow_html=True)

def display_file_upload():
    """Section d'upload moderne"""
    st.markdown("""
    <div class="section slide-up">
        <div class="section-title">📁 Fichiers d'analyse</div>
        <div class="upload-container">
            <div class="upload-box">
                <div class="upload-label">💻 Code source</div>
                <div style="font-size: 0.85rem; color: #64748b; margin-top: 0.5rem;">
                    Glissez votre fichier ici ou cliquez pour sélectionner
                </div>
            </div>
            <div class="upload-box">
                <div class="upload-label">🚨 Fichier d'erreur</div>
                <div style="font-size: 0.85rem; color: #64748b; margin-top: 0.5rem;">
                    Logs, traces ou rapports d'erreur
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        program_file = st.file_uploader(
            "Sélectionner le fichier programme",
            help="Formats supportés: .c, .cpp, .py, .js, etc.",
            key="program",
            label_visibility="collapsed"
        )
        
        if program_file:
            st.markdown(f'<div class="status-message status-success">✓ {program_file.name} chargé</div>', unsafe_allow_html=True)
    
    with col2:
        error_file = st.file_uploader(
            "Sélectionner le fichier d'erreur",
            help="Formats supportés: .log, .error, .txt, etc.",
            key="error",
            label_visibility="collapsed"
        )
        
        if error_file:
            st.markdown(f'<div class="status-message status-success">✓ {error_file.name} chargé</div>', unsafe_allow_html=True)
    
    return program_file, error_file

def display_analysis_progress():
    """Barre de progression minimaliste"""
    progress_placeholder = st.empty()
    
    steps = [
        "Lecture des fichiers",
        "Analyse du code source", 
        "Traitement des erreurs",
        "Génération du rapport"
    ]
    
    for i, step in enumerate(steps):
        progress = (i + 1) / len(steps) * 100
        progress_placeholder.markdown(f"""
        <div class="section fade-in">
            <div style="font-size: 0.9rem; color: #475569; margin-bottom: 0.5rem;">{step}...</div>
            <div class="progress-container">
                <div class="progress-bar" style="width: {progress}%"></div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        time.sleep(0.6)
    
    progress_placeholder.empty()

def display_metrics(results):
    """Métriques épurées"""
    st.markdown(f"""
    <div class="section">
        <div class="section-title">📊 Résultats d'analyse</div>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">{len(results['error_entries'])}</div>
                <div class="metric-label">Erreurs détectées</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{len(results['code_errors'])}</div>
                <div class="metric-label">Problèmes code</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{results['code_structure']['total_lines']}</div>
                <div class="metric-label">Lignes analysées</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{len(results['code_structure']['functions'])}</div>
                <div class="metric-label">Fonctions</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def generate_detailed_analysis(main_error):
    """Génère une analyse détaillée comme dans l'exemple"""
    error_msg = main_error.message
    timestamp = main_error.timestamp if main_error.timestamp else "Non spécifié"

    if "locked" in error_msg.lower() and "ORDER_LOOP" in error_msg:
        return f"""
**Le message d'erreur détecté :**

```
{timestamp} CAOFORS ERROR {error_msg}
```

signifie que la tâche ORDER_LOOP est déjà en cours d'exécution lorsqu'un autre processus a tenté de la lancer. Voici une explication plus détaillée :

**🔍 Analyse de l'erreur :**

• **task [ORDER_LOOP] locked** : Cela indique que la tâche nommée ORDER_LOOP est verrouillée.

• **(already runs)** : Elle est déjà en cours d'exécution, donc une seconde instance ne peut pas démarrer.

• **EXIT.** : Le processus a quitté sans exécuter la tâche à nouveau pour éviter un conflit.

**✅ Causes possibles :**

• **Exécution en double** : Un précédent lancement de ORDER_LOOP n'est pas encore terminé.

• **Blocage ou crash** : La tâche précédente est peut-être bloquée ou a planté sans libérer le verrou.

• **Pas de gestion de timeout** : Le verrou est toujours considéré actif même si le processus est mort ou bloqué.

• **Défaut de synchronisation dans l'application** : Mauvaise gestion de tâches concurrentes.

**🛠️ Solutions possibles :**

• **Vérifier si ORDER_LOOP tourne encore** : Peut-être via un outil de monitoring ou un processus actif.

• **Libérer manuellement le verrou** : Si possible, selon le système, vider le fichier de lock ou réinitialiser l'état.

• **Ajouter une vérification de durée** : Mettre en place un timeout ou une vérification pour forcer l'arrêt du verrou après X minutes.

• **Revoir la logique de lancement** : Empêcher le lancement simultané si la tâche tourne encore sans avoir besoin de forcer une erreur.
        """

    elif "error" in error_msg.lower():
        return f"""
**Le message d'erreur détecté :**

```
{timestamp} {error_msg}
```

**🔍 Analyse de l'erreur :**

• **Type d'erreur** : {main_error.error_type.value}

• **Sévérité** : {main_error.severity}

• **Contexte** : Erreur survenue lors de l'exécution du programme

**✅ Causes possibles :**

• **Erreur de syntaxe** : Problème dans le code source

• **Dépendance manquante** : Module ou bibliothèque non disponible

• **Problème de configuration** : Paramètres incorrects ou manquants

• **Ressource indisponible** : Fichier, réseau ou service inaccessible

**🛠️ Solutions possibles :**

• **Vérifier la syntaxe** : Examiner le code autour de la ligne d'erreur

• **Contrôler les dépendances** : S'assurer que tous les modules requis sont installés

• **Vérifier la configuration** : Valider les paramètres et chemins d'accès

• **Tester l'environnement** : S'assurer que toutes les ressources sont disponibles
        """

    else:
        return f"""
**Le message détecté :**

```
{timestamp} {error_msg}
```

**🔍 Analyse générale :**

• **Message** : {error_msg}

• **Type** : {main_error.error_type.value}

• **Sévérité** : {main_error.severity}

**✅ Recommandations générales :**

• **Examiner le contexte** : Vérifier les conditions au moment de l'erreur

• **Consulter la documentation** : Rechercher des informations sur ce type de message

• **Vérifier les logs** : Examiner les messages précédents et suivants

• **Tester en isolation** : Reproduire le problème dans un environnement contrôlé
        """

def display_results(results):
    """Affichage des résultats avec analyse détaillée"""
    if not results['error_entries']:
        st.markdown("""
        <div class="section">
            <div class="section-title">ℹ️ Aucune erreur détectée</div>
            <div class="result-content">L'analyse n'a pas détecté d'erreurs dans les fichiers fournis.</div>
        </div>
        """, unsafe_allow_html=True)
        return

    main_error = results['error_entries'][0]

    # Analyse détaillée
    detailed_analysis = generate_detailed_analysis(main_error)

    st.markdown("""
    <div class="section">
        <div class="section-title">🤖 Analyse de l'Agent IA</div>
    </div>
    """, unsafe_allow_html=True)

    # Afficher l'analyse détaillée
    st.markdown(detailed_analysis)

    # Informations supplémentaires si plusieurs erreurs
    if len(results['error_entries']) > 1:
        st.markdown(f"""
        <div class="section">
            <div class="section-title">📋 Erreurs supplémentaires</div>
            <div class="result-content">
                {len(results['error_entries']) - 1} autre(s) erreur(s) détectée(s) dans le fichier.
                L'analyse ci-dessus se concentre sur l'erreur principale.
            </div>
        </div>
        """, unsafe_allow_html=True)

def main():
    """Fonction principale"""
    
    # Initialisation
    init_session_state()
    
    # Header
    display_header()
    
    # Upload
    program_file, error_file = display_file_upload()
    
    # Analyse
    if program_file and error_file:
        if st.button("Analyser", type="primary"):
            
            # Progress
            display_analysis_progress()
            
            # Lecture des fichiers
            try:
                program_content = program_file.getvalue().decode('utf-8')
            except:
                program_content = program_file.getvalue().decode('latin-1')
                
            try:
                error_content = error_file.getvalue().decode('utf-8')
            except:
                error_content = error_file.getvalue().decode('latin-1')
            
            # Analyse
            error_entries = st.session_state.error_analyzer.analyze_error_file(error_content)
            error_summary = st.session_state.error_analyzer.get_error_summary(error_entries)
            code_errors = st.session_state.code_analyzer.analyze_code(program_content, "c")
            code_structure = st.session_state.code_analyzer.analyze_structure(program_content)
            
            results = {
                'error_entries': error_entries,
                'error_summary': error_summary,
                'code_errors': code_errors,
                'code_structure': code_structure
            }
            
            # Affichage
            st.markdown('<div class="status-message status-info">✓ Analyse terminée</div>', unsafe_allow_html=True)
            
            display_metrics(results)
            display_results(results)

if __name__ == "__main__":
    main()
