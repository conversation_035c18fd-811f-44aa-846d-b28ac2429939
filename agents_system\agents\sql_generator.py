#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent SQL Generator - Génère des scripts SQL à partir de spécifications
"""

import re
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

class SQLType(Enum):
    """Types de requêtes SQL"""
    CREATE_TABLE = "CREATE_TABLE"
    INSERT = "INSERT"
    SELECT = "SELECT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    ALTER_TABLE = "ALTER_TABLE"
    CREATE_INDEX = "CREATE_INDEX"
    CREATE_VIEW = "CREATE_VIEW"
    STORED_PROCEDURE = "STORED_PROCEDURE"

class DataType(Enum):
    """Types de données SQL"""
    VARCHAR = "VARCHAR"
    INT = "INT"
    BIGINT = "BIGINT"
    DECIMAL = "DECIMAL"
    DATE = "DATE"
    DATETIME = "DATETIME"
    TIMESTAMP = "TIMESTAMP"
    BOOLEAN = "BOOLEAN"
    TEXT = "TEXT"
    BLOB = "BLOB"

@dataclass
class Column:
    """Représente une colonne de table"""
    name: str
    data_type: DataType
    length: Optional[int] = None
    precision: Optional[int] = None
    scale: Optional[int] = None
    nullable: bool = True
    primary_key: bool = False
    foreign_key: Optional[str] = None
    default_value: Optional[str] = None
    auto_increment: bool = False
    unique: bool = False

@dataclass
class Table:
    """Représente une table"""
    name: str
    columns: List[Column]
    description: Optional[str] = None

@dataclass
class SQLScript:
    """Représente un script SQL généré"""
    sql_type: SQLType
    content: str
    description: str
    table_name: Optional[str] = None
    estimated_execution_time: Optional[str] = None

class SQLGenerator:
    """Générateur de scripts SQL à partir de spécifications"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supported_databases = ["MySQL", "PostgreSQL", "SQL Server", "Oracle", "SQLite"]
        
    def parse_specifications(self, spec_content: str) -> Dict[str, Any]:
        """Parse les spécifications et extrait les informations"""
        try:
            specs = {
                'tables': [],
                'relationships': [],
                'indexes': [],
                'views': [],
                'procedures': [],
                'database_type': 'MySQL'  # Par défaut
            }
            
            # Détecter le type de base de données
            db_patterns = {
                'mysql': r'(?i)(mysql|mariadb)',
                'postgresql': r'(?i)(postgresql|postgres)',
                'sqlserver': r'(?i)(sql\s*server|mssql)',
                'oracle': r'(?i)oracle',
                'sqlite': r'(?i)sqlite'
            }
            
            for db_type, pattern in db_patterns.items():
                if re.search(pattern, spec_content):
                    specs['database_type'] = db_type.title()
                    break
            
            # Extraire les définitions de tables
            table_matches = re.finditer(
                r'(?i)table\s+(\w+)\s*[:\-\{]?\s*(.*?)(?=table\s+\w+|$)', 
                spec_content, 
                re.DOTALL
            )
            
            for match in table_matches:
                table_name = match.group(1)
                table_content = match.group(2)
                
                table = self._parse_table_definition(table_name, table_content)
                if table:
                    specs['tables'].append(table)
            
            # Extraire les relations
            relation_patterns = [
                r'(?i)(\w+)\s+references?\s+(\w+)',
                r'(?i)foreign\s+key\s+(\w+)\s+references?\s+(\w+)',
                r'(?i)(\w+)\s+→\s+(\w+)',
                r'(?i)(\w+)\s+->\s+(\w+)'
            ]
            
            for pattern in relation_patterns:
                relations = re.findall(pattern, spec_content)
                for rel in relations:
                    specs['relationships'].append({
                        'from_table': rel[0],
                        'to_table': rel[1]
                    })
            
            return specs
            
        except Exception as e:
            self.logger.error(f"Erreur lors du parsing des spécifications: {e}")
            return {'tables': [], 'relationships': [], 'database_type': 'MySQL'}
    
    def _parse_table_definition(self, table_name: str, content: str) -> Optional[Table]:
        """Parse la définition d'une table"""
        try:
            columns = []
            
            # Patterns pour extraire les colonnes
            column_patterns = [
                r'(?i)(\w+)\s*:\s*(\w+)(?:\((\d+)(?:,(\d+))?\))?\s*(.*)',
                r'(?i)(\w+)\s+(\w+)(?:\((\d+)(?:,(\d+))?\))?\s*(.*)',
                r'(?i)-\s*(\w+)\s*:\s*(\w+)(?:\((\d+)(?:,(\d+))?\))?\s*(.*)'
            ]
            
            for pattern in column_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    col_name = match.group(1)
                    col_type = match.group(2).upper()
                    length = int(match.group(3)) if match.group(3) else None
                    scale = int(match.group(4)) if match.group(4) else None
                    attributes = match.group(5) if match.group(5) else ""
                    
                    # Mapper les types
                    data_type = self._map_data_type(col_type)
                    
                    # Analyser les attributs
                    nullable = 'not null' not in attributes.lower()
                    primary_key = any(keyword in attributes.lower() 
                                    for keyword in ['primary key', 'pk', 'primary'])
                    auto_increment = any(keyword in attributes.lower() 
                                       for keyword in ['auto_increment', 'identity', 'serial'])
                    unique = 'unique' in attributes.lower()
                    
                    # Extraire la valeur par défaut
                    default_match = re.search(r'default\s+([^\s,]+)', attributes.lower())
                    default_value = default_match.group(1) if default_match else None
                    
                    column = Column(
                        name=col_name,
                        data_type=data_type,
                        length=length,
                        scale=scale,
                        nullable=nullable,
                        primary_key=primary_key,
                        auto_increment=auto_increment,
                        unique=unique,
                        default_value=default_value
                    )
                    columns.append(column)
            
            if columns:
                return Table(name=table_name, columns=columns)
            return None
            
        except Exception as e:
            self.logger.error(f"Erreur lors du parsing de la table {table_name}: {e}")
            return None
    
    def _map_data_type(self, type_str: str) -> DataType:
        """Mappe les types de données"""
        type_mapping = {
            'VARCHAR': DataType.VARCHAR,
            'STRING': DataType.VARCHAR,
            'TEXT': DataType.TEXT,
            'INT': DataType.INT,
            'INTEGER': DataType.INT,
            'BIGINT': DataType.BIGINT,
            'LONG': DataType.BIGINT,
            'DECIMAL': DataType.DECIMAL,
            'NUMERIC': DataType.DECIMAL,
            'FLOAT': DataType.DECIMAL,
            'DOUBLE': DataType.DECIMAL,
            'DATE': DataType.DATE,
            'DATETIME': DataType.DATETIME,
            'TIMESTAMP': DataType.TIMESTAMP,
            'BOOLEAN': DataType.BOOLEAN,
            'BOOL': DataType.BOOLEAN,
            'BLOB': DataType.BLOB,
            'BINARY': DataType.BLOB
        }
        
        return type_mapping.get(type_str.upper(), DataType.VARCHAR)
    
    def generate_create_table_scripts(self, tables: List[Table], database_type: str = "MySQL") -> List[SQLScript]:
        """Génère les scripts CREATE TABLE"""
        scripts = []
        
        for table in tables:
            sql_content = self._generate_create_table_sql(table, database_type)
            
            script = SQLScript(
                sql_type=SQLType.CREATE_TABLE,
                content=sql_content,
                description=f"Création de la table {table.name}",
                table_name=table.name,
                estimated_execution_time="< 1 seconde"
            )
            scripts.append(script)
        
        return scripts
    
    def _generate_create_table_sql(self, table: Table, database_type: str) -> str:
        """Génère le SQL CREATE TABLE pour une table"""
        sql_lines = [f"CREATE TABLE {table.name} ("]
        
        column_definitions = []
        primary_keys = []
        
        for column in table.columns:
            col_def = f"    {column.name} {self._format_column_type(column, database_type)}"
            
            if not column.nullable:
                col_def += " NOT NULL"
            
            if column.auto_increment:
                if database_type.lower() == 'mysql':
                    col_def += " AUTO_INCREMENT"
                elif database_type.lower() == 'postgresql':
                    col_def += " SERIAL"
                elif database_type.lower() == 'sqlserver':
                    col_def += " IDENTITY(1,1)"
            
            if column.default_value:
                col_def += f" DEFAULT {column.default_value}"
            
            if column.unique and not column.primary_key:
                col_def += " UNIQUE"
            
            column_definitions.append(col_def)
            
            if column.primary_key:
                primary_keys.append(column.name)
        
        sql_lines.extend([col + "," for col in column_definitions])
        
        if primary_keys:
            pk_def = f"    PRIMARY KEY ({', '.join(primary_keys)})"
            sql_lines.append(pk_def)
        else:
            # Enlever la dernière virgule
            sql_lines[-1] = sql_lines[-1].rstrip(',')
        
        sql_lines.append(");")
        
        return "\n".join(sql_lines)
    
    def _format_column_type(self, column: Column, database_type: str) -> str:
        """Formate le type de colonne selon la base de données"""
        type_str = column.data_type.value
        
        if column.data_type in [DataType.VARCHAR, DataType.TEXT] and column.length:
            type_str = f"{type_str}({column.length})"
        elif column.data_type == DataType.DECIMAL and column.length and column.scale:
            type_str = f"{type_str}({column.length},{column.scale})"
        elif column.data_type == DataType.DECIMAL and column.length:
            type_str = f"{type_str}({column.length},2)"
        
        return type_str
    
    def generate_insert_scripts(self, tables: List[Table]) -> List[SQLScript]:
        """Génère des scripts INSERT d'exemple"""
        scripts = []
        
        for table in tables:
            # Générer des données d'exemple
            sample_data = self._generate_sample_data(table)
            
            if sample_data:
                columns = [col.name for col in table.columns if not col.auto_increment]
                values_list = []
                
                for row in sample_data:
                    values = [f"'{val}'" if isinstance(val, str) else str(val) for val in row]
                    values_list.append(f"    ({', '.join(values)})")
                
                sql_content = f"""INSERT INTO {table.name} ({', '.join(columns)})
VALUES
{',\n'.join(values_list)};"""
                
                script = SQLScript(
                    sql_type=SQLType.INSERT,
                    content=sql_content,
                    description=f"Insertion de données d'exemple dans {table.name}",
                    table_name=table.name,
                    estimated_execution_time="< 1 seconde"
                )
                scripts.append(script)
        
        return scripts
    
    def _generate_sample_data(self, table: Table) -> List[List]:
        """Génère des données d'exemple pour une table"""
        sample_data = []
        
        # Générer 3 lignes d'exemple
        for i in range(1, 4):
            row = []
            for column in table.columns:
                if column.auto_increment:
                    continue
                
                if column.data_type == DataType.VARCHAR:
                    if 'name' in column.name.lower():
                        row.append(f"Exemple_{i}")
                    elif 'email' in column.name.lower():
                        row.append(f"user{i}@example.com")
                    else:
                        row.append(f"Valeur_{i}")
                elif column.data_type in [DataType.INT, DataType.BIGINT]:
                    row.append(i * 10)
                elif column.data_type == DataType.DECIMAL:
                    row.append(f"{i * 10}.50")
                elif column.data_type == DataType.DATE:
                    row.append(f"2024-01-{i:02d}")
                elif column.data_type == DataType.DATETIME:
                    row.append(f"2024-01-{i:02d} 10:00:00")
                elif column.data_type == DataType.BOOLEAN:
                    row.append(i % 2 == 1)
                else:
                    row.append(f"Valeur_{i}")
            
            sample_data.append(row)
        
        return sample_data
