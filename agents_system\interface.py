"""
Interface web Streamlit pour le système d'agents d'analyse d'erreurs
"""

import streamlit as st
import sys
import os
import time
import json
from pathlib import Path
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime

# Ajouter le répertoire parent au path
sys.path.append(str(Path(__file__).parent))

from config.config import DEFAULT_CONFIG
from agents.error_analyzer_agent import ErrorAnalyzerAgent
from tools.code_analyzer import CodeAnalyzer
from tools.error_analyzer import ErrorAnalyzer
from utils.file_handler import FileHandler

# Configuration de la page
st.set_page_config(
    page_title="Système d'Agents - Analyse d'Erreurs",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS personnalisé - Design Premium LEONI
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    /* Variables CSS */
    :root {
        --leoni-blue: #002857;
        --leoni-orange: #ff7514;
        --leoni-light-blue: #003366;
        --leoni-dark-blue: #001a33;
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-soft: 0 8px 32px rgba(0, 40, 87, 0.15);
        --shadow-strong: 0 16px 64px rgba(0, 40, 87, 0.25);
        --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Masquer les éléments Streamlit */
    .stDeployButton, #MainMenu, header[data-testid="stHeader"],
    footer, [data-testid="stToolbar"] {
        display: none !important;
        visibility: hidden !important;
    }

    /* Arrière-plan avec effet parallax */
    .stApp {
        background:
            linear-gradient(135deg, var(--leoni-blue) 0%, var(--leoni-light-blue) 50%, #004080 100%);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        position: relative;
        min-height: 100vh;
    }

    .stApp::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 20%, rgba(255, 117, 20, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(0, 40, 87, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, rgba(255, 117, 20, 0.05) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: backgroundFloat 20s ease-in-out infinite;
    }

    @keyframes backgroundFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(1deg); }
    }

    /* Navbar Premium avec Glassmorphism */
    .navbar {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 1.2rem 2.5rem;
        border-radius: 20px;
        margin: 1.5rem 0 3rem 0;
        box-shadow: var(--shadow-soft);
        border: 1px solid var(--glass-border);
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        overflow: hidden;
        animation: slideInDown 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .navbar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 2s ease;
    }

    .navbar:hover::before {
        left: 100%;
    }

    .navbar-brand {
        font-size: 1.6rem;
        font-weight: 800;
        color: var(--leoni-blue);
        background: linear-gradient(135deg, var(--leoni-blue) 0%, var(--leoni-orange) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: -0.02em;
        position: relative;
        z-index: 2;
    }

    .navbar-nav {
        display: flex;
        gap: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .nav-link {
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        text-decoration: none;
        color: var(--leoni-blue);
        font-weight: 600;
        font-size: 0.95rem;
        transition: var(--transition-smooth);
        position: relative;
        overflow: hidden;
        border: 2px solid transparent;
    }

    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        opacity: 0;
        transition: var(--transition-smooth);
        z-index: -1;
    }

    .nav-link:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 117, 20, 0.3);
        border-color: var(--leoni-orange);
    }

    .nav-link:hover::before {
        opacity: 1;
    }

    .nav-link.active {
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        color: white;
        box-shadow: 0 6px 20px rgba(255, 117, 20, 0.4);
        transform: translateY(-1px);
    }

    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Sidebar */
    .css-1d391kg {
        background: #001a33 !important;
    }

    /* Titre principal avec effet premium */
    .main-header {
        font-size: 3.5rem;
        font-weight: 800;
        color: white;
        text-align: center;
        margin: 3rem 0;
        background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        letter-spacing: -0.03em;
        line-height: 1.1;
        animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
        position: relative;
    }

    .main-header::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        border-radius: 2px;
        animation: expandWidth 1s ease-out 1s both;
    }

    @keyframes expandWidth {
        from { width: 0; }
        to { width: 100px; }
    }

    /* Conteneurs avec Glassmorphism Premium */
    .stContainer > div, .element-container {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border-radius: 20px !important;
        padding: 2rem !important;
        margin: 1.5rem 0 !important;
        box-shadow: var(--shadow-soft) !important;
        border: 1px solid var(--glass-border) !important;
        transition: var(--transition-smooth) !important;
        position: relative !important;
        overflow: hidden !important;
        animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
    }

    .stContainer > div::before, .element-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--leoni-orange), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stContainer > div:hover, .element-container:hover {
        transform: translateY(-5px) !important;
        box-shadow: var(--shadow-strong) !important;
        border-color: rgba(255, 117, 20, 0.3) !important;
    }

    .stContainer > div:hover::before, .element-container:hover::before {
        opacity: 1;
    }

    /* Boutons Premium avec Micro-interactions */
    .stButton > button {
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 50px !important;
        padding: 1rem 2.5rem !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        font-size: 1.1rem !important;
        letter-spacing: 0.02em !important;
        box-shadow:
            0 8px 25px rgba(255, 117, 20, 0.3),
            0 4px 10px rgba(0, 0, 0, 0.1) !important;
        transition: var(--transition-smooth) !important;
        position: relative !important;
        overflow: hidden !important;
        text-transform: uppercase !important;
    }

    .stButton > button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
    }

    .stButton > button:hover {
        background: linear-gradient(135deg, #ff8f44 0%, var(--leoni-orange) 100%) !important;
        transform: translateY(-3px) scale(1.02) !important;
        box-shadow:
            0 12px 35px rgba(255, 117, 20, 0.4),
            0 8px 15px rgba(0, 0, 0, 0.15) !important;
    }

    .stButton > button:hover::before {
        left: 100%;
    }

    .stButton > button:active {
        transform: translateY(-1px) scale(1.01) !important;
        transition: all 0.1s ease !important;
    }

    .stButton > button:focus {
        outline: none !important;
        box-shadow:
            0 8px 25px rgba(255, 117, 20, 0.3),
            0 0 0 3px rgba(255, 117, 20, 0.2) !important;
    }

    /* Boîtes d'information */
    .success-box {
        padding: 1.5rem;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.95);
        border-left: 4px solid #ff7514;
        color: #002857;
        margin: 1rem 0;
        box-shadow: 0 4px 12px rgba(255, 117, 20, 0.2);
    }

    .error-box {
        padding: 1.5rem;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.95);
        border-left: 4px solid #dc3545;
        color: #721c24;
        margin: 1rem 0;
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
    }

    .info-box {
        padding: 1.5rem;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.95);
        border-left: 4px solid #002857;
        color: #002857;
        margin: 1rem 0;
        box-shadow: 0 4px 12px rgba(0, 40, 87, 0.2);
        line-height: 1.6;
    }

    /* Zone d'upload */
    .stFileUploader {
        background: rgba(255, 255, 255, 0.95) !important;
        border: 2px dashed #ff7514 !important;
        border-radius: 15px !important;
        padding: 2rem !important;
    }

    .stFileUploader:hover {
        border-color: #ff8f44 !important;
        background: rgba(255, 255, 255, 0.98) !important;
    }

    /* Métriques */
    [data-testid="metric-container"] {
        background: linear-gradient(135deg, #002857 0%, #003366 100%) !important;
        color: white !important;
        border-radius: 10px !important;
        padding: 1rem !important;
        box-shadow: 0 4px 12px rgba(0, 40, 87, 0.3) !important;
    }

    /* Graphiques */
    .js-plotly-plot {
        border-radius: 10px !important;
        box-shadow: 0 4px 12px rgba(0, 40, 87, 0.2) !important;
        background: rgba(255, 255, 255, 0.95) !important;
    }

    /* Animations d'entrée */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @keyframes shimmer {
        0% { background-position: -200px 0; }
        100% { background-position: calc(200px + 100%) 0; }
    }

    /* Typographie Premium */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        letter-spacing: -0.02em !important;
        color: var(--leoni-blue) !important;
    }

    p, div, span {
        font-family: 'Inter', sans-serif !important;
        line-height: 1.7 !important;
        color: rgba(0, 40, 87, 0.8) !important;
    }

    /* Effets de loading */
    .loading-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 2s infinite;
    }

    /* Scrollbar personnalisée */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(0, 40, 87, 0.1);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        border-radius: 4px;
        transition: background 0.3s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #ff8f44 0%, var(--leoni-orange) 100%);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .navbar {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
        }

        .navbar-nav {
            flex-wrap: wrap;
            justify-content: center;
        }

        .main-header {
            font-size: 2.5rem;
        }

        .stContainer > div {
            padding: 1rem !important;
            margin: 0.5rem 0 !important;
        }
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """Initialise l'état de session"""
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = None
    if 'file_handler' not in st.session_state:
        st.session_state.file_handler = FileHandler()
    if 'code_analyzer' not in st.session_state:
        st.session_state.code_analyzer = CodeAnalyzer()
    if 'error_analyzer' not in st.session_state:
        st.session_state.error_analyzer = ErrorAnalyzer()

def display_navbar():
    """Affiche la navbar de navigation avec transitions fluides"""
    st.markdown("""
    <div class="navbar">
        <div class="navbar-brand">🤖 LEONI AI Agents</div>
        <div class="navbar-nav">
            <a href="#" onclick="navigateToPage('http://localhost:8502')" class="nav-link active">
                <span class="nav-icon">🔍</span>
                <span class="nav-text">Analyse d'Erreurs</span>
            </a>
            <a href="#" onclick="navigateToPage('http://localhost:8504')" class="nav-link">
                <span class="nav-icon">🗄️</span>
                <span class="nav-text">SQL Generator</span>
            </a>
            <a href="#" onclick="showComingSoon('Agent 3')" class="nav-link">
                <span class="nav-icon">📊</span>
                <span class="nav-text">Agent 3</span>
            </a>
            <a href="#" onclick="showComingSoon('Agent 4')" class="nav-link">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">Agent 4</span>
            </a>
        </div>
    </div>

    <!-- Overlay de transition -->
    <div id="transition-overlay" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #002857 0%, #003366 100%);
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    ">
        <div style="
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 117, 20, 0.3);
            border-top: 4px solid #ff7514;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        "></div>
        <div style="
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            font-family: 'Inter', sans-serif;
        ">Chargement...</div>
    </div>

    <script>
        // Animation de rotation pour le loader
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .nav-link {
                display: flex !important;
                align-items: center !important;
                gap: 0.5rem !important;
            }

            .nav-icon {
                font-size: 1.1rem;
                transition: transform 0.3s ease;
            }

            .nav-link:hover .nav-icon {
                transform: scale(1.2) rotate(5deg);
            }

            .nav-text {
                font-size: 0.95rem;
            }
        `;
        document.head.appendChild(style);

        // Fonction de navigation avec transition fluide
        function navigateToPage(url) {
            const overlay = document.getElementById('transition-overlay');

            // Afficher l'overlay avec animation
            overlay.style.visibility = 'visible';
            overlay.style.opacity = '1';

            // Attendre l'animation puis naviguer
            setTimeout(() => {
                window.location.href = url;
            }, 300);
        }

        // Fonction pour les agents à venir
        function showComingSoon(agentName) {
            // Créer une notification élégante
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                padding: 2rem 3rem;
                border-radius: 20px;
                box-shadow: 0 16px 64px rgba(0, 40, 87, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.2);
                z-index: 10000;
                text-align: center;
                font-family: 'Inter', sans-serif;
                animation: popIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            `;

            notification.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 1rem;">🚀</div>
                <h3 style="color: #002857; margin-bottom: 0.5rem; font-weight: 700;">${agentName}</h3>
                <p style="color: rgba(0, 40, 87, 0.7); margin-bottom: 1.5rem;">Bientôt disponible !</p>
                <button onclick="this.parentElement.remove()" style="
                    background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%);
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 25px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">OK</button>
            `;

            // Ajouter l'animation CSS
            const animationStyle = document.createElement('style');
            animationStyle.textContent = `
                @keyframes popIn {
                    0% {
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.8);
                    }
                    100% {
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }
                }
            `;
            document.head.appendChild(animationStyle);

            document.body.appendChild(notification);

            // Supprimer automatiquement après 3 secondes
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // Masquer l'overlay au chargement de la page
        window.addEventListener('load', () => {
            const overlay = document.getElementById('transition-overlay');
            overlay.style.opacity = '0';
            overlay.style.visibility = 'hidden';
        });
    </script>
    """, unsafe_allow_html=True)

def display_header():
    """Affiche un en-tête professionnel et épuré pour l'application"""
    st.markdown("""
    <div style="text-align: center; margin: 2.5rem 0 2.5rem 0;">
        <img src=\"https://cdn-icons-png.flaticon.com/512/4712/4712035.png\" width=\"80\" style=\"margin-bottom: 1rem; filter: drop-shadow(0 4px 16px #00285733);\" alt=\"Logo IA\" />
        <h1 class=\"main-header\" style=\"margin-bottom: 0.5rem;\">
            LEONI AI Agents
        </h1>
        <div style=\"font-size: 1.25rem; color: #ff7514; font-weight: 600; margin-bottom: 0.5rem; letter-spacing: 0.01em;\">
            Système d'Analyse Automatisée &amp; Intelligence Artificielle
        </div>
        <div style=\"font-size: 1.05rem; color: rgba(255,255,255,0.85); font-weight: 400; margin-bottom: 1.2rem;\">
            Détectez, comprenez et corrigez vos erreurs de code en un clic.
        </div>
    </div>
    <div style=\"max-width: 900px; margin: 0 auto 2.5rem auto; background: rgba(255,255,255,0.97); border-radius: 18px; box-shadow: 0 8px 32px rgba(0,40,87,0.10); padding: 2.2rem 2.5rem 1.5rem 2.5rem; border: 1px solid var(--glass-border);\">
        <div style=\"display: flex; flex-wrap: wrap; gap: 2.2rem; justify-content: center; align-items: flex-start;\">
            <div style=\"flex: 1 1 260px; min-width: 220px;\">
                <h3 style=\"color: #002857; font-size: 1.13rem; font-weight: 700; margin-bottom: 0.3rem;\">À propos</h3>
                <p style=\"color: #444; font-size: 1.01rem; margin-bottom: 0.3rem;\">LEONI AI Agents est une plateforme intelligente qui analyse vos fichiers de code et d'erreurs, localise les problèmes et propose des solutions concrètes grâce à l'IA.</p>
                <ul style=\"color: #002857; font-size: 0.98rem; margin: 0 0 0 1.2rem; padding: 0; list-style: disc;\">
                    <li>Analyse multi-langages</li>
                    <li>Suggestions de correction instantanées</li>
                    <li>Interface moderne et intuitive</li>
                </ul>
            </div>
            <div style=\"flex: 1 1 260px; min-width: 220px;\">
                <h3 style=\"color: #002857; font-size: 1.13rem; font-weight: 700; margin-bottom: 0.3rem;\">Comment ça marche ?</h3>
                <div style=\"display: flex; flex-direction: column; gap: 0.4rem; font-size: 0.98rem;\">
                    <div style=\"display: flex; align-items: center; gap: 0.5rem;\"><span style=\"font-size:1.15rem;\">📤</span> Déposez vos fichiers de code et d'erreur</div>
                    <div style=\"display: flex; align-items: center; gap: 0.5rem;\"><span style=\"font-size:1.15rem;\">🤖</span> L'IA analyse et détecte les erreurs</div>
                    <div style=\"display: flex; align-items: center; gap: 0.5rem;\"><span style=\"font-size:1.15rem;\">💡</span> Recevez des solutions et recommandations</div>
                </div>
            </div>
            <div style=\"flex: 1 1 180px; min-width: 150px; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 0.7rem;\">
                <div style=\"font-size: 2.1rem;\">✨</div>
                <div style=\"color: #ff7514; font-weight: 700; font-size: 1.01rem; text-align: center;\">100% Automatisé<br>et sécurisé</div>
                <a href=\"#analyse\" style=\"text-decoration: none;\">
                    <button style=\"background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%); color: white; border: none; border-radius: 50px; padding: 0.7rem 2rem; font-size: 1.05rem; font-weight: 700; box-shadow: 0 8px 25px rgba(255, 117, 20, 0.13); cursor: pointer; transition: background 0.3s; margin-top: 0.5rem;\">
                        🚀 Commencer l'analyse
                    </button>
                </a>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def sidebar_config():
    """Configuration simplifiée - valeurs par défaut"""
    # Configuration forcée sans interface utilisateur
    analysis_mode = "Analyse avec ChatGPT 4.1"
    api_key = "sk-proj-pUkzo4FarIw0n_-w1T0Uq6DR32i0sr--LQnkNG8QDL0ZI97wdYP7p6Ca8tq6f1MLW5QXK7bWx9T3BLbkFJ-xhLBLQP8egicmVRslfJ1Ghw21eAKTBGBherW0V-6AZiBGyQSS0HDoNJ-j93TmE8d_FHNrYA"
    model = "gpt-4-turbo-preview"
    temperature = 0.1
    show_code_structure = True
    show_error_timeline = True
    max_errors_display = 20

    return {
        'analysis_mode': analysis_mode,
        'api_key': api_key,
        'model': model,
        'temperature': temperature,
        'show_code_structure': show_code_structure,
        'show_error_timeline': show_error_timeline,
        'max_errors_display': max_errors_display
    }

def detect_file_language(filename):
    """Détecte le langage de programmation basé sur l'extension du fichier"""
    extension = filename.lower().split('.')[-1] if '.' in filename else ''

    language_map = {
        'c': 'c',
        'cpp': 'cpp',
        'cxx': 'cpp',
        'cc': 'cpp',
        'h': 'c',
        'hpp': 'cpp',
        'py': 'python',
        'java': 'java',
        'js': 'javascript',
        'ts': 'typescript',
        'html': 'html',
        'css': 'css',
        'sql': 'sql',
        'xml': 'xml',
        'json': 'json',
        'yaml': 'yaml',
        'yml': 'yaml',
        'sh': 'bash',
        'bat': 'batch',
        'ps1': 'powershell',
        'php': 'php',
        'rb': 'ruby',
        'go': 'go',
        'rs': 'rust',
        'swift': 'swift',
        'kt': 'kotlin',
        'scala': 'scala',
        'r': 'r',
        'matlab': 'matlab',
        'm': 'matlab',
        'pl': 'perl',
        'lua': 'lua',
        'dart': 'dart',
        'asm': 'assembly',
        's': 'assembly',
        'log': 'text',
        'txt': 'text',
        'error': 'text'
    }

    return language_map.get(extension, 'text')

def display_file_upload():
    """Section d'upload compacte et professionnelle"""
    col1, col2 = st.columns(2)

    with col1:
        program_file = st.file_uploader(
            "📄 Fichier Programme",
            help="Tous types de fichiers acceptés (code source, scripts, etc.)",
            key="program_file"
        )
        if program_file:
            st.success(f"✅ Fichier chargé: {program_file.name}")
            file_size = len(program_file.getvalue())
            st.info(f"📊 Taille: {file_size:,} octets")
            with st.expander("👁️ Aperçu du fichier"):
                try:
                    content = program_file.read().decode('utf-8')
                    language = detect_file_language(program_file.name)
                    st.code(content[:500] + "..." if len(content) > 500 else content, language=language)
                    program_file.seek(0)  # Reset file pointer
                except UnicodeDecodeError:
                    try:
                        program_file.seek(0)
                        content = program_file.read().decode('latin-1')
                        language = detect_file_language(program_file.name)
                        st.code(content[:500] + "..." if len(content) > 500 else content, language=language)
                        program_file.seek(0)
                    except:
                        st.warning("⚠️ Fichier binaire ou encodage non supporté pour l'aperçu")
                        st.info("💡 Le fichier sera tout de même analysé")

    with col2:
        error_file = st.file_uploader(
            "🚨 Fichier d'Erreur",
            help="Tous types de fichiers acceptés (logs, erreurs, traces, etc.)",
            key="error_file"
        )
        if error_file:
            st.success(f"✅ Fichier chargé: {error_file.name}")
            file_size = len(error_file.getvalue())
            st.info(f"📊 Taille: {file_size:,} octets")
            with st.expander("👁️ Aperçu du fichier"):
                try:
                    content = error_file.read().decode('utf-8')
                    st.code(content[:500] + "..." if len(content) > 500 else content, language="text")
                    error_file.seek(0)  # Reset file pointer
                except UnicodeDecodeError:
                    try:
                        error_file.seek(0)
                        content = error_file.read().decode('latin-1')
                        st.code(content[:500] + "..." if len(content) > 500 else content, language="text")
                        error_file.seek(0)
                    except:
                        st.warning("⚠️ Fichier binaire ou encodage non supporté pour l'aperçu")
                        st.info("💡 Le fichier sera tout de même analysé")

    return program_file, error_file

def analyze_files(program_file, error_file):
    """Analyse les fichiers - fonction réutilisable"""
    from config.config import DEFAULT_CONFIG
    config = DEFAULT_CONFIG
    return analyze_files_with_config(program_file, error_file, config)

def analyze_files_with_config(program_file, error_file, config):
    """Analyse les fichiers avec configuration"""
    try:
        # Lire le contenu des fichiers uploadés
        try:
            program_content = program_file.getvalue().decode('utf-8')
        except UnicodeDecodeError:
            program_content = program_file.getvalue().decode('latin-1')

        try:
            error_content = error_file.getvalue().decode('utf-8')
        except UnicodeDecodeError:
            error_content = error_file.getvalue().decode('latin-1')

        program_name = program_file.name
        error_name = error_file.name

        # Analyse locale
        with st.spinner("🔍 Analyse en cours..."):
            # Analyser le code
            code_errors = st.session_state.code_analyzer.analyze_code(program_content, "c")
            code_structure = st.session_state.code_analyzer.analyze_structure(program_content)

            # Analyser les erreurs
            error_entries = st.session_state.error_analyzer.analyze_error_file(error_content)
            error_summary = st.session_state.error_analyzer.get_error_summary(error_entries)
            error_categories = st.session_state.error_analyzer.categorize_errors(error_entries)

        # Préparer les résultats
        results = {
            'program_name': program_name,
            'error_name': error_name,
            'program_content': program_content,
            'error_content': error_content,
            'code_errors': code_errors,
            'code_structure': code_structure,
            'error_entries': error_entries,
            'error_summary': error_summary,
            'error_categories': error_categories,
            'chatgpt_analysis': None  # Pas d'analyse ChatGPT dans cette version
        }

        return results

    except Exception as e:
        st.error(f"❌ Erreur lors de l'analyse: {str(e)}")
        return None

def file_upload_section():
    """Section de téléchargement de fichiers"""
    st.header("📁 Téléchargement des Fichiers")

    st.info("💡 **Tous types de fichiers sont acceptés** - L'interface détectera automatiquement le format")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📄 Fichier Programme")
        program_file = st.file_uploader(
            "Choisissez un fichier programme",
            help="Tous types de fichiers acceptés (code source, scripts, etc.)",
            key="program_file"
        )

        if program_file:
            st.success(f"✅ Fichier chargé: {program_file.name}")
            file_size = len(program_file.getvalue())
            st.info(f"📊 Taille: {file_size:,} octets")

            # Aperçu du fichier
            with st.expander("👁️ Aperçu du fichier"):
                try:
                    content = program_file.read().decode('utf-8')
                    language = detect_file_language(program_file.name)
                    st.code(content[:500] + "..." if len(content) > 500 else content, language=language)
                    program_file.seek(0)  # Reset file pointer
                except UnicodeDecodeError:
                    try:
                        # Essayer avec d'autres encodages
                        program_file.seek(0)
                        content = program_file.read().decode('latin-1')
                        language = detect_file_language(program_file.name)
                        st.code(content[:500] + "..." if len(content) > 500 else content, language=language)
                        program_file.seek(0)
                    except:
                        st.warning("⚠️ Fichier binaire ou encodage non supporté pour l'aperçu")
                        st.info("💡 Le fichier sera tout de même analysé")

    with col2:
        st.subheader("🚨 Fichier d'Erreur")
        error_file = st.file_uploader(
            "Choisissez un fichier d'erreur",
            help="Tous types de fichiers acceptés (logs, erreurs, traces, etc.)",
            key="error_file"
        )

        if error_file:
            st.success(f"✅ Fichier chargé: {error_file.name}")
            file_size = len(error_file.getvalue())
            st.info(f"📊 Taille: {file_size:,} octets")

            # Aperçu du fichier
            with st.expander("👁️ Aperçu du fichier"):
                try:
                    content = error_file.read().decode('utf-8')
                    st.code(content[:500] + "..." if len(content) > 500 else content, language="text")
                    error_file.seek(0)  # Reset file pointer
                except UnicodeDecodeError:
                    try:
                        # Essayer avec d'autres encodages
                        error_file.seek(0)
                        content = error_file.read().decode('latin-1')
                        st.code(content[:500] + "..." if len(content) > 500 else content, language="text")
                        error_file.seek(0)
                    except:
                        st.warning("⚠️ Fichier binaire ou encodage non supporté pour l'aperçu")
                        st.info("💡 Le fichier sera tout de même analysé")

    return program_file, error_file

def analyze_files(program_file, error_file, config):
    """Analyse les fichiers"""
    try:
        # Lire le contenu des fichiers uploadés
        try:
            program_content = program_file.getvalue().decode('utf-8')
        except UnicodeDecodeError:
            program_content = program_file.getvalue().decode('latin-1')

        try:
            error_content = error_file.getvalue().decode('utf-8')
        except UnicodeDecodeError:
            error_content = error_file.getvalue().decode('latin-1')

        program_name = program_file.name
        error_name = error_file.name
        
        # Analyse locale
        with st.spinner("🔍 Analyse en cours..."):
            # Analyser le code
            code_errors = st.session_state.code_analyzer.analyze_code(program_content, "c")
            code_structure = st.session_state.code_analyzer.analyze_structure(program_content)
            
            # Analyser les erreurs
            error_entries = st.session_state.error_analyzer.analyze_error_file(error_content)
            error_summary = st.session_state.error_analyzer.get_error_summary(error_entries)
            error_categories = st.session_state.error_analyzer.categorize_errors(error_entries)
        
        # Analyse ChatGPT (si configurée)
        chatgpt_analysis = None
        if config['analysis_mode'] == "Analyse avec ChatGPT 4.1" and config['api_key']:
            try:
                with st.spinner("🤖 Analyse en cours..."):
                    from tools.chatgpt_client import ChatGPTClient
                    client = ChatGPTClient(config['api_key'], config['model'])
                    response = client.analyze_code_errors(
                        program_content=program_content[:8000],  # Limiter pour éviter les tokens
                        error_content=error_content,
                        agent_prompt=DEFAULT_CONFIG.get_agent_config('error_analyzer')['prompt']
                    )
                    chatgpt_analysis = response.content
            except Exception as e:
                if "Connection error" in str(e) or "connection" in str(e).lower():
                    st.info("🌐 **Connexion IA indisponible** - Analyse locale activée automatiquement\n\n"
                           "Le réseau d'entreprise bloque l'accès à l'IA. L'analyse locale fournit déjà "
                           "une détection complète des erreurs, leur localisation et des suggestions de solutions.")
                else:
                    st.warning(f"⚠️ Erreur IA: {str(e)}\n\nBasculement vers l'analyse locale...")
        
        return {
            'program_name': program_name,
            'error_name': error_name,
            'program_content': program_content,
            'error_content': error_content,
            'code_errors': code_errors,
            'code_structure': code_structure,
            'error_entries': error_entries,
            'error_summary': error_summary,
            'error_categories': error_categories,
            'chatgpt_analysis': chatgpt_analysis,
            'analysis_time': datetime.now()
        }
        
    except Exception as e:
        st.error(f"❌ Erreur lors de l'analyse: {str(e)}")
        return None

def display_results(results):
    """Affiche les résultats d'analyse - fonction réutilisable"""
    if not results:
        return

    # Utiliser la configuration par défaut
    from config.config import DEFAULT_CONFIG
    config = DEFAULT_CONFIG

    display_analysis_results(results, config)

def display_analysis_results(results, config):
    """Affiche les résultats d'analyse"""
    if not results:
        return

    st.header("📊 Résultats d'Analyse")

    # Informations générales
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("📄 Fichier Programme", results['program_name'])
    with col2:
        st.metric("🚨 Fichier d'Erreur", results['error_name'])
    with col3:
        st.metric("⏰ Heure d'Analyse", results['analysis_time'].strftime("%H:%M:%S"))
    with col4:
        st.metric("🔍 Mode", config['analysis_mode'].split()[0])

    # Résumé des erreurs
    st.subheader("📈 Résumé des Erreurs")

    error_summary = results['error_summary']

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "🚨 Total Erreurs Fichier",
            error_summary['total_errors'],
            help="Nombre d'erreurs détectées dans le fichier d'erreur"
        )

    with col2:
        st.metric(
            "⚠️ Problèmes Code",
            len(results['code_errors']),
            help="Nombre de problèmes détectés dans le code"
        )

    with col3:
        st.metric(
            "📏 Lignes de Code",
            results['code_structure']['code_lines'],
            help="Nombre de lignes de code (hors commentaires et lignes vides)"
        )

    # Graphiques de répartition
    if error_summary['total_errors'] > 0:
        col1, col2 = st.columns(2)

        with col1:
            if error_summary['by_severity']:
                # Graphique par sévérité
                severity_df = pd.DataFrame(
                    list(error_summary['by_severity'].items()),
                    columns=['Sévérité', 'Nombre']
                )
                fig_severity = px.pie(
                    severity_df,
                    values='Nombre',
                    names='Sévérité',
                    title="Répartition par Sévérité",
                    color_discrete_map={
                        'LOW': '#28a745',
                        'MEDIUM': '#ffc107',
                        'HIGH': '#fd7e14',
                        'CRITICAL': '#dc3545'
                    }
                )
                st.plotly_chart(fig_severity, use_container_width=True)

        with col2:
            if error_summary['by_type']:
                # Graphique par type
                type_df = pd.DataFrame(
                    list(error_summary['by_type'].items()),
                    columns=['Type', 'Nombre']
                )
                fig_type = px.bar(
                    type_df,
                    x='Type',
                    y='Nombre',
                    title="Répartition par Type d'Erreur",
                    color='Nombre',
                    color_continuous_scale='Reds'
                )
                fig_type.update_xaxis(tickangle=45)
                st.plotly_chart(fig_type, use_container_width=True)

    # Structure du code
    if config['show_code_structure']:
        st.subheader("🏗️ Structure du Code")

        structure = results['code_structure']

        col1, col2 = st.columns(2)

        with col1:
            # Métriques de structure
            st.metric("📊 Lignes Totales", structure['total_lines'])
            st.metric("💬 Lignes Commentaires", structure['comment_lines'])
            st.metric("📝 Lignes Vides", structure['blank_lines'])

        with col2:
            st.metric("🔧 Fonctions", len(structure['functions']))
            st.metric("📦 Includes", len(structure['includes']))

            # Ratio commentaires/code
            if structure['code_lines'] > 0:
                comment_ratio = (structure['comment_lines'] / structure['code_lines']) * 100
                st.metric("📊 Ratio Commentaires", f"{comment_ratio:.1f}%")

        # Graphique de composition
        composition_data = {
            'Type': ['Code', 'Commentaires', 'Lignes vides'],
            'Lignes': [structure['code_lines'], structure['comment_lines'], structure['blank_lines']]
        }
        composition_df = pd.DataFrame(composition_data)

        fig_composition = px.bar(
            composition_df,
            x='Type',
            y='Lignes',
            title="Composition du Fichier",
            color='Type',
            color_discrete_map={
                'Code': '#1f77b4',
                'Commentaires': '#2ca02c',
                'Lignes vides': '#d62728'
            }
        )
        st.plotly_chart(fig_composition, use_container_width=True)

    # Timeline des erreurs
    if config['show_error_timeline'] and error_summary['timeline']:
        st.subheader("⏰ Timeline des Erreurs")

        timeline_df = pd.DataFrame(error_summary['timeline'])
        timeline_df['timestamp'] = pd.to_datetime(timeline_df['timestamp'])

        fig_timeline = px.scatter(
            timeline_df,
            x='timestamp',
            y='severity',
            color='severity',
            title="Timeline des Erreurs",
            hover_data=['message'],
            color_discrete_map={
                'LOW': '#28a745',
                'MEDIUM': '#ffc107',
                'HIGH': '#fd7e14',
                'CRITICAL': '#dc3545'
            }
        )
        st.plotly_chart(fig_timeline, use_container_width=True)

    # Détails des erreurs
    display_error_details(results, config)

    # Analyse ChatGPT
    if results['chatgpt_analysis']:
        st.subheader("🤖 Analyse ChatGPT 4.1")
        st.markdown(results['chatgpt_analysis'])

    # Recommandations
    display_recommendations(results)

def display_error_details(results, config):
    """Affiche les détails des erreurs"""
    st.subheader("🔍 Détails des Erreurs")

    # Onglets pour différents types d'erreurs
    tab1, tab2 = st.tabs(["🚨 Erreurs du Fichier Log", "⚠️ Problèmes du Code"])

    with tab1:
        error_entries = results['error_entries']
        if error_entries:
            for i, error in enumerate(error_entries[:config['max_errors_display']], 1):
                with st.expander(f"Erreur {i}: {error.message}", expanded=(i <= 3)):
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write(f"**Type:** {error.error_type.value}")
                        st.write(f"**Sévérité:** {error.severity}")
                        st.write(f"**Ligne:** {error.line_number}")

                    with col2:
                        if error.timestamp:
                            st.write(f"**Timestamp:** {error.timestamp}")
                        st.write(f"**Détails:** {error.details}")

                    st.code(error.raw_line, language="text")
        else:
            st.info("Aucune erreur détectée dans le fichier d'erreur")

    with tab2:
        code_errors = results['code_errors']
        if code_errors:
            for i, error in enumerate(code_errors[:config['max_errors_display']], 1):
                with st.expander(f"Problème {i}: {error.message}", expanded=(i <= 3)):
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write(f"**Type:** {error.error_type}")
                        st.write(f"**Sévérité:** {error.severity.value}")
                        st.write(f"**Ligne:** {error.line_number}")

                    with col2:
                        st.write(f"**Suggestion:** {error.suggestion}")

                    st.code(error.code_snippet, language="c")
        else:
            st.info("Aucun problème détecté dans le code")

def display_recommendations(results):
    """Affiche les recommandations"""
    st.subheader("💡 Recommandations")

    # Analyse spécifique pour l'erreur principale
    error_entries = results['error_entries']
    if error_entries:
        main_error = error_entries[0]  # Première erreur

        if "locked" in main_error.raw_line.lower() and "already runs" in main_error.raw_line.lower():
            st.markdown("""
            <div class="info-box">
                <h4>🎯 Diagnostic Principal</h4>
                <p><strong>Erreur détectée:</strong> Processus déjà en cours d'exécution</p>
                <p><strong>Cause:</strong> Un autre processus CAOFORS avec la même tâche est déjà actif</p>
                <p><strong>Impact:</strong> Le programme ne peut pas démarrer</p>
            </div>
            """, unsafe_allow_html=True)

            st.markdown("""
            <div class="success-box">
                <h4>🛠️ Solutions Recommandées</h4>
                <ol>
                    <li><strong>Vérifier les processus:</strong> <code>tasklist | findstr caofors</code></li>
                    <li><strong>Arrêter le processus:</strong> <code>taskkill /PID &lt;pid&gt; /F</code></li>
                    <li><strong>Nettoyer les verrous:</strong> <code>del /Q C:\\temp\\cao*\\*.lock</code></li>
                    <li><strong>Redémarrer le service</strong> si nécessaire</li>
                </ol>
            </div>
            """, unsafe_allow_html=True)

    # Recommandations générales pour le code
    code_errors = results['code_errors']
    if code_errors:
        critical_errors = [e for e in code_errors if e.severity.value == 'CRITICAL']
        high_errors = [e for e in code_errors if e.severity.value == 'HIGH']

        if critical_errors or high_errors:
            st.warning(f"⚠️ Attention: {len(critical_errors)} erreurs critiques et {len(high_errors)} erreurs importantes détectées")

            st.markdown("""
            **Priorités de correction:**
            1. **Erreurs critiques** - À corriger immédiatement
            2. **Erreurs importantes** - À corriger rapidement
            3. **Erreurs moyennes** - À planifier
            4. **Erreurs mineures** - Amélioration continue
            """)

def main():
    """Fonction principale de l'interface"""
    init_session_state()
    display_navbar()
    display_header()

    # Configuration
    config = sidebar_config()

    # Section de téléchargement
    program_file, error_file = file_upload_section()

    # Bouton d'analyse
    if st.button("🚀 Lancer l'Analyse", type="primary", use_container_width=True):
        if program_file and error_file:
            results = analyze_files(program_file, error_file, config)
            if results:
                st.session_state.analysis_results = results
                st.success("✅ Analyse terminée avec succès!")
        else:
            st.error("❌ Veuillez sélectionner les deux fichiers avant de lancer l'analyse")

    # Affichage des résultats
    if st.session_state.analysis_results:
        display_analysis_results(st.session_state.analysis_results, config)

if __name__ == "__main__":
    main()
