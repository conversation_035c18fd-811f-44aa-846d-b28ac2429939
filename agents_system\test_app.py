#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple de l'application LEONI AI Agent
"""

import streamlit as st

# Configuration de la page
st.set_page_config(
    page_title="LEONI AI Agent - Test",
    page_icon="🤖",
    layout="wide"
)

# CSS LEONI simple
st.markdown("""
<style>
    .stApp {
        background: white;
        font-family: 'Inter', sans-serif;
    }
    
    .main-header {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        border-top: 4px solid #002857;
    }
    
    .title {
        color: #002857;
        font-size: 2rem;
        font-weight: 700;
        text-align: center;
        margin: 0;
    }
    
    .subtitle {
        color: #64748b;
        text-align: center;
        margin-top: 0.5rem;
    }
    
    .stButton > button {
        background: #ff7514 !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
    }
    
    .stButton > button:hover {
        background: #e6660a !important;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <div class="title">🤖 LEONI AI Agent</div>
        <div class="subtitle">Test de l'application - Analyse d'erreurs</div>
    </div>
    """, unsafe_allow_html=True)
    
    # Test simple
    st.header("📁 Test de Fonctionnement")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📄 Fichier Programme")
        program_file = st.file_uploader("Code source", key="program")
        if program_file:
            st.success(f"✅ {program_file.name} chargé")
    
    with col2:
        st.subheader("🚨 Fichier d'Erreur")
        error_file = st.file_uploader("Fichier d'erreur", key="error")
        if error_file:
            st.success(f"✅ {error_file.name} chargé")
    
    # Test du bouton
    if st.button("🚀 Test Analyse", type="primary"):
        if program_file and error_file:
            st.success("✅ Application fonctionne correctement !")
            
            # Simulation d'analyse
            st.header("🤖 Résultat de Test")
            st.markdown("""
            **Le test confirme que l'application fonctionne :**
            
            🔍 **Analyse de test :**
            • Upload de fichiers : ✅ Fonctionnel
            • Interface utilisateur : ✅ Responsive  
            • Boutons : ✅ Interactifs
            • Styles LEONI : ✅ Appliqués
            
            ✅ **Statut :**
            • Application : Opérationnelle
            • Design : Conforme LEONI
            • Fonctionnalités : Actives
            """)
        else:
            st.warning("⚠️ Veuillez charger les deux fichiers pour tester")
    
    # Informations de debug
    st.markdown("---")
    st.subheader("🔧 Informations de Debug")
    st.write(f"**Port :** 8500")
    st.write(f"**Status :** Application active")
    st.write(f"**URL :** http://localhost:8500")

if __name__ == "__main__":
    main()
