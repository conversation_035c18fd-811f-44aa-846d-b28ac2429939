<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation - LEONI AI Agents</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #002857 0%, #003366 50%, #004080 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 117, 20, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 40, 87, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 117, 20, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            animation: backgroundFloat 20s ease-in-out infinite;
        }
        
        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(1deg); }
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 16px 64px rgba(0, 40, 87, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            max-width: 700px;
            animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        h1 {
            font-size: 2.8rem;
            font-weight: 800;
            color: #002857;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #002857 0%, #ff7514 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            color: rgba(0, 40, 87, 0.7);
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .url-display {
            background: rgba(0, 40, 87, 0.05);
            border: 2px solid rgba(255, 117, 20, 0.2);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            color: #002857;
            font-weight: 600;
        }
        
        .buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1.2rem 2rem;
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            font-size: 1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(255, 117, 20, 0.3);
            cursor: pointer;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }
        
        .btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 35px rgba(255, 117, 20, 0.4);
            background: linear-gradient(135deg, #ff8f44 0%, #ff7514 100%);
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:active {
            transform: translateY(-1px) scale(1.01);
        }
        
        .icon {
            font-size: 1.3rem;
            transition: transform 0.3s ease;
        }
        
        .btn:hover .icon {
            transform: scale(1.2) rotate(5deg);
        }
        
        .info-box {
            background: rgba(255, 117, 20, 0.1);
            border: 1px solid rgba(255, 117, 20, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: left;
        }
        
        .info-box h3 {
            color: #ff7514;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .info-box ul {
            color: rgba(0, 40, 87, 0.8);
            line-height: 1.6;
            padding-left: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.5rem;
        }
        
        /* Overlay de transition */
        .transition-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #002857 0%, #003366 100%);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .transition-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .loader {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 117, 20, 0.3);
            border-top: 4px solid #ff7514;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loader-text {
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 LEONI AI Agents</h1>
        <p class="subtitle">Navigation unifiée sur un seul port avec routing intelligent</p>
        
        <div class="url-display">
            <strong>URL Base:</strong> http://localhost:8500
        </div>
        
        <div class="buttons">
            <button class="btn" onclick="navigateToPage('http://localhost:8500?page=analyzer')">
                <span class="icon">🔍</span>
                <span>Analyse d'Erreurs</span>
            </button>
            
            <button class="btn" onclick="navigateToPage('http://localhost:8500?page=sql-generator')">
                <span class="icon">🗄️</span>
                <span>SQL Generator</span>
            </button>
        </div>
        
        <div class="info-box">
            <h3>✨ Fonctionnalités de Navigation</h3>
            <ul>
                <li><strong>Port unique:</strong> Tous les agents sur localhost:8500</li>
                <li><strong>Routing URL:</strong> ?page=analyzer ou ?page=sql-generator</li>
                <li><strong>Transitions fluides:</strong> Overlay de chargement élégant</li>
                <li><strong>Navigation cohérente:</strong> Navbar intégrée sur chaque page</li>
                <li><strong>Design uniforme:</strong> Thème LEONI sur toutes les interfaces</li>
            </ul>
        </div>
    </div>
    
    <!-- Overlay de transition -->
    <div class="transition-overlay" id="transition-overlay">
        <div class="loader"></div>
        <div class="loader-text">Chargement de l'agent...</div>
    </div>
    
    <script>
        function navigateToPage(url) {
            const overlay = document.getElementById('transition-overlay');
            
            // Afficher l'overlay avec animation
            overlay.classList.add('active');
            
            // Attendre l'animation puis naviguer
            setTimeout(() => {
                window.location.href = url;
            }, 800);
        }
        
        // Masquer l'overlay au chargement de la page
        window.addEventListener('load', () => {
            const overlay = document.getElementById('transition-overlay');
            overlay.classList.remove('active');
        });
    </script>
</body>
</html>
