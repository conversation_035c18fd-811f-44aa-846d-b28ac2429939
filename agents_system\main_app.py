#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application principale - LEONI AI Agents
Gère les deux agents sur le même port avec routing
"""

import streamlit as st
import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(str(Path(__file__).parent))

# Configuration de la page
st.set_page_config(
    page_title="LEONI AI Agents",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# CSS Premium LEONI
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
    
    /* Variables CSS */
    :root {
        --leoni-blue: #002857;
        --leoni-orange: #ff7514;
        --leoni-light-blue: #003366;
        --leoni-dark-blue: #001a33;
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-soft: 0 8px 32px rgba(0, 40, 87, 0.15);
        --shadow-strong: 0 16px 64px rgba(0, 40, 87, 0.25);
        --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Masquer les éléments Streamlit */
    .stDeployButton, #MainMenu, header[data-testid="stHeader"], 
    footer, [data-testid="stToolbar"] {
        display: none !important;
        visibility: hidden !important;
    }

    /* Arrière-plan avec effet parallax */
    .stApp {
        background: 
            linear-gradient(135deg, var(--leoni-blue) 0%, var(--leoni-light-blue) 50%, #004080 100%);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        position: relative;
        min-height: 100vh;
    }
    
    .stApp::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: 
            radial-gradient(circle at 20% 20%, rgba(255, 117, 20, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(0, 40, 87, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, rgba(255, 117, 20, 0.05) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
        animation: backgroundFloat 20s ease-in-out infinite;
    }
    
    @keyframes backgroundFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(1deg); }
    }

    /* Navbar Premium avec Glassmorphism */
    .navbar {
        background: transparent !important;
        box-shadow: none !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }
    /* Supprimer le fond et l'ombre du parent de la navbar */
    .stApp > div:first-child, .stApp > div:first-child > div:first-child {
        background: transparent !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .navbar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 2s ease;
    }
    
    .navbar:hover::before {
        left: 100%;
    }
    
    .navbar-brand {
        font-size: 1.6rem;
        font-weight: 800;
        color: var(--leoni-blue);
        background: linear-gradient(135deg, var(--leoni-blue) 0%, var(--leoni-orange) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: -0.02em;
        position: relative;
        z-index: 2;
    }
    
    .navbar-nav {
        display: flex;
        gap: 0.5rem;
        position: relative;
        z-index: 2;
    }
    
    .nav-link {
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        text-decoration: none;
        color: var(--leoni-blue);
        font-weight: 600;
        font-size: 0.95rem;
        transition: var(--transition-smooth);
        position: relative;
        overflow: hidden;
        border: 2px solid transparent;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        opacity: 0;
        transition: var(--transition-smooth);
        z-index: -1;
    }
    
    .nav-link:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 117, 20, 0.3);
        border-color: var(--leoni-orange);
    }
    
    .nav-link:hover::before {
        opacity: 1;
    }
    
    .nav-link.active {
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        color: white;
        box-shadow: 0 6px 20px rgba(255, 117, 20, 0.4);
        transform: translateY(-1px);
    }
    
    .nav-icon {
        font-size: 1.1rem;
        transition: transform 0.3s ease;
    }
    
    .nav-link:hover .nav-icon {
        transform: scale(1.2) rotate(5deg);
    }
    
    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Titre principal avec effet premium */
    .main-header {
        font-size: 3.5rem;
        font-weight: 800;
        color: white;
        text-align: center;
        margin: 3rem 0;
        background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 50%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        letter-spacing: -0.03em;
        line-height: 1.1;
        animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
        position: relative;
    }
    
    .main-header::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%);
        border-radius: 2px;
        animation: expandWidth 1s ease-out 1s both;
    }
    
    @keyframes expandWidth {
        from { width: 0; }
        to { width: 100px; }
    }

    /* Conteneurs avec Glassmorphism Premium */
    .stContainer, .block-container, .stApp > div, .stApp > div > div, .stApp > div > div > div {
        background: transparent !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .stContainer > div, .element-container {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(20px) !important;
        border-radius: 20px !important;
        padding: 2rem !important;
        margin: 1.5rem 0 !important;
        box-shadow: var(--shadow-soft) !important;
        border: 1px solid var(--glass-border) !important;
        transition: var(--transition-smooth) !important;
        position: relative !important;
        overflow: hidden !important;
        animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
    }
    
    .stContainer > div::before, .element-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--leoni-orange), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .stContainer > div:hover, .element-container:hover {
        transform: translateY(-5px) !important;
        box-shadow: var(--shadow-strong) !important;
        border-color: rgba(255, 117, 20, 0.3) !important;
    }
    
    .stContainer > div:hover::before, .element-container:hover::before {
        opacity: 1;
    }

    /* Boutons Premium avec Micro-interactions */
    .stButton > button {
        background: linear-gradient(135deg, var(--leoni-orange) 0%, #ff8f44 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 50px !important;
        padding: 1rem 2.5rem !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        font-size: 1.1rem !important;
        letter-spacing: 0.02em !important;
        box-shadow: 
            0 8px 25px rgba(255, 117, 20, 0.3),
            0 4px 10px rgba(0, 0, 0, 0.1) !important;
        transition: var(--transition-smooth) !important;
        position: relative !important;
        overflow: hidden !important;
        text-transform: uppercase !important;
    }
    
    .stButton > button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
    }
    
    .stButton > button:hover {
        background: linear-gradient(135deg, #ff8f44 0%, var(--leoni-orange) 100%) !important;
        transform: translateY(-3px) scale(1.02) !important;
        box-shadow: 
            0 12px 35px rgba(255, 117, 20, 0.4),
            0 8px 15px rgba(0, 0, 0, 0.15) !important;
    }
    
    .stButton > button:hover::before {
        left: 100%;
    }

    /* Animations d'entrée */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Typographie Premium */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        letter-spacing: -0.02em !important;
        color: var(--leoni-blue) !important;
    }
    
    p, div, span {
        font-family: 'Inter', sans-serif !important;
        line-height: 1.7 !important;
        color: rgba(0, 40, 87, 0.8) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .navbar {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
        }
        
        .navbar-nav {
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .main-header {
            font-size: 2.5rem;
        }
    }
    header[data-testid="stHeader"] {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
        min-height: 0 !important;
        max-height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        background: transparent !important;
        box-shadow: none !important;
    }
    /* Supprimer l'espace réservé par le header */
    .block-container {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
</style>
""", unsafe_allow_html=True)

def get_query_params():
    """Récupère les paramètres de l'URL"""
    try:
        return st.query_params
    except:
        return {}

def display_navbar(current_page="analyzer"):
    """Affiche la navbar de navigation"""
    st.markdown(f"""
    <div class="navbar">
        <div class="navbar-brand">🤖 LEONI AI Agents</div>
        <div class="navbar-nav">
            <div class="nav-link {'active' if current_page == 'analyzer' else ''}" data-page="analyzer">
                <span class="nav-icon">🔍</span>
                <span class="nav-text">Analyse d'Erreurs</span>
            </div>
            <div class="nav-link {'active' if current_page == 'sql-generator' else ''}" data-page="sql-generator">
                <span class="nav-icon">🗄️</span>
                <span class="nav-text">SQL Generator</span>
            </div>
            <div class="nav-link" onclick="showComingSoon('Agent 3')">
                <span class="nav-icon">📊</span>
                <span class="nav-text">Agent 3</span>
            </div>
            <div class="nav-link" onclick="showComingSoon('Agent 4')">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">Agent 4</span>
            </div>
        </div>
    </div>
    
    <script>
        function navigateToPage(page) {{
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            window.location.href = url.toString();
        }}

        // Ajouter les event listeners après le chargement
        document.addEventListener('DOMContentLoaded', function() {{
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {{
                link.addEventListener('click', function(e) {{
                    e.preventDefault();
                    const page = this.getAttribute('data-page');
                    if (page) {{
                        navigateToPage(page);
                    }}
                }});
            }});
        }});
        
        function showComingSoon(agentName) {{
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                padding: 2rem 3rem;
                border-radius: 20px;
                box-shadow: 0 16px 64px rgba(0, 40, 87, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.2);
                z-index: 10000;
                text-align: center;
                font-family: 'Inter', sans-serif;
                animation: popIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            `;
            
            notification.innerHTML = `
                <div style="font-size: 3rem; margin-bottom: 1rem;">🚀</div>
                <h3 style="color: #002857; margin-bottom: 0.5rem; font-weight: 700;">${{agentName}}</h3>
                <p style="color: rgba(0, 40, 87, 0.7); margin-bottom: 1.5rem;">Bientôt disponible !</p>
                <button onclick="this.parentElement.remove()" style="
                    background: linear-gradient(135deg, #ff7514 0%, #ff8f44 100%);
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 25px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">OK</button>
            `;
            
            const animationStyle = document.createElement('style');
            animationStyle.textContent = `
                @keyframes popIn {{
                    0% {{
                        opacity: 0;
                        transform: translate(-50%, -50%) scale(0.8);
                    }}
                    100% {{
                        opacity: 1;
                        transform: translate(-50%, -50%) scale(1);
                    }}
                }}
            `;
            document.head.appendChild(animationStyle);
            
            document.body.appendChild(notification);
            
            setTimeout(() => {{
                if (notification.parentElement) {{
                    notification.remove();
                }}
            }}, 3000);
        }}
    </script>
    """, unsafe_allow_html=True)

def main():
    """Fonction principale avec routing"""
    
    # Récupérer le paramètre de page
    query_params = get_query_params()
    current_page = query_params.get('page', 'analyzer')
    
    # Afficher la navbar
    display_navbar(current_page)
    
    # Router vers la bonne page
    if current_page == 'sql-generator':
        # Interface SQL Generator
        show_sql_generator_page()
    
    else:
        # Interface Analyzer (par défaut)
        show_analyzer_page()

def show_analyzer_page():
    """Affiche la page d'analyse d'erreurs"""
    from interface import display_header, display_file_upload, analyze_files, display_results, init_session_state

    # Initialisation
    init_session_state()

    # Interface
    display_header()

    # Upload de fichiers
    program_file, error_file = display_file_upload()

    # Analyse
    if program_file and error_file and st.button("🚀 Analyser les fichiers", type="primary"):
        results = analyze_files(program_file, error_file)
        if results:
            st.session_state.analysis_results = results
            st.success("✅ Analyse terminée avec succès !")

    # Affichage des résultats
    if hasattr(st.session_state, 'analysis_results') and st.session_state.analysis_results:
        st.markdown("---")
        display_results(st.session_state.analysis_results)

def show_sql_generator_page():
    """Affiche la page SQL Generator"""
    from sql_interface import display_header, display_file_upload, display_database_selection, analyze_specifications, display_results, init_session_state
    from utils.file_handler import FileHandler

    # Initialisation
    init_session_state()

    # Interface
    display_header()

    # Upload de fichier
    uploaded_file = display_file_upload()

    # Configuration
    database_type, include_sample_data = display_database_selection()

    # Analyse
    if uploaded_file is not None:
        if st.button("🚀 Générer les scripts SQL", type="primary"):
            try:
                # Lire le contenu du fichier
                file_handler = FileHandler()
                file_content = file_handler.read_uploaded_file(uploaded_file)

                if file_content:
                    # Analyser et générer
                    results = analyze_specifications(file_content, database_type)

                    if results and not include_sample_data:
                        # Supprimer les scripts INSERT si non demandés
                        results['insert_scripts'] = []

                    st.session_state.generated_scripts = results

                    if results:
                        st.success("✅ Scripts SQL générés avec succès !")
                    else:
                        st.error("❌ Erreur lors de la génération des scripts")
                else:
                    st.error("❌ Impossible de lire le fichier")

            except Exception as e:
                st.error(f"❌ Erreur: {str(e)}")

    # Affichage des résultats
    if hasattr(st.session_state, 'generated_scripts') and st.session_state.generated_scripts:
        st.markdown("---")
        display_results(st.session_state.generated_scripts)

if __name__ == "__main__":
    main()
