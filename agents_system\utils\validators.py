"""
Utilitaires de validation pour le système d'agents
"""

import os
import re
from typing import List, Dict, Any, Optional
from pathlib import Path

class FileValidator:
    """Validateur de fichiers"""
    
    @staticmethod
    def validate_file_exists(file_path: str) -> bool:
        """Vérifie si un fichier existe"""
        return os.path.exists(file_path) and os.path.isfile(file_path)
    
    @staticmethod
    def validate_file_readable(file_path: str) -> bool:
        """Vérifie si un fichier est lisible"""
        try:
            with open(file_path, 'r') as f:
                f.read(1)
            return True
        except Exception:
            return False
    
    @staticmethod
    def validate_file_extension(file_path: str, allowed_extensions: List[str]) -> bool:
        """Vérifie si l'extension du fichier est autorisée"""
        path_obj = Path(file_path)
        return path_obj.suffix.lower() in [ext.lower() for ext in allowed_extensions]
    
    @staticmethod
    def validate_file_size(file_path: str, max_size_mb: float = 50.0) -> bool:
        """Vérifie si la taille du fichier est acceptable"""
        try:
            size_bytes = os.path.getsize(file_path)
            size_mb = size_bytes / (1024 * 1024)
            return size_mb <= max_size_mb
        except Exception:
            return False

class ContentValidator:
    """Validateur de contenu"""
    
    @staticmethod
    def is_program_file(content: str) -> bool:
        """Détermine si le contenu ressemble à un fichier de programme"""
        program_indicators = [
            r'#include\s*<',  # Includes C/C++
            r'#define\s+',    # Defines
            r'int\s+main\s*\(',  # Fonction main
            r'EXEC\s+SQL',    # SQL embarqué
            r'void\s+\w+\s*\(',  # Fonctions void
            r'struct\s+\w+',  # Structures
            r'typedef\s+',    # Typedefs
            r'/\*.*?\*/',     # Commentaires C
            r'//.*$',         # Commentaires C++
        ]
        
        for pattern in program_indicators:
            if re.search(pattern, content, re.MULTILINE | re.IGNORECASE):
                return True
        return False
    
    @staticmethod
    def is_error_file(content: str) -> bool:
        """Détermine si le contenu ressemble à un fichier d'erreur"""
        error_indicators = [
            r'\d{2}\.\d{2}\.\d{4}\s+\d{2}:\d{2}:\d{2}',  # Timestamp
            r'ERROR',
            r'FAILED',
            r'EXCEPTION',
            r'FATAL',
            r'CRITICAL',
            r'locked',
            r'EXIT',
        ]
        
        for pattern in error_indicators:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False
    
    @staticmethod
    def extract_error_messages(content: str) -> List[str]:
        """Extrait les messages d'erreur du contenu"""
        error_patterns = [
            r'ERROR[:\s]+(.+)',
            r'FAILED[:\s]+(.+)',
            r'EXCEPTION[:\s]+(.+)',
            r'FATAL[:\s]+(.+)',
            r'CRITICAL[:\s]+(.+)',
        ]
        
        errors = []
        for pattern in error_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            errors.extend(matches)
        
        return [error.strip() for error in errors if error.strip()]
    
    @staticmethod
    def extract_timestamps(content: str) -> List[str]:
        """Extrait les timestamps du contenu"""
        timestamp_pattern = r'\d{2}\.\d{2}\.\d{4}\s+\d{2}:\d{2}:\d{2}'
        return re.findall(timestamp_pattern, content)

class ConfigValidator:
    """Validateur de configuration"""
    
    @staticmethod
    def validate_openai_config(config: Dict[str, Any]) -> List[str]:
        """Valide la configuration OpenAI"""
        errors = []
        
        if not config.get('OPENAI_API_KEY'):
            errors.append("OPENAI_API_KEY est requis")
        
        if not config.get('OPENAI_MODEL'):
            errors.append("OPENAI_MODEL est requis")
        
        max_tokens = config.get('OPENAI_MAX_TOKENS', 0)
        if not isinstance(max_tokens, int) or max_tokens <= 0:
            errors.append("OPENAI_MAX_TOKENS doit être un entier positif")
        
        temperature = config.get('OPENAI_TEMPERATURE', -1)
        if not isinstance(temperature, (int, float)) or temperature < 0 or temperature > 2:
            errors.append("OPENAI_TEMPERATURE doit être entre 0 et 2")
        
        return errors
    
    @staticmethod
    def validate_agent_config(agent_config: Dict[str, Any]) -> List[str]:
        """Valide la configuration d'un agent"""
        errors = []
        
        required_fields = ['name', 'role', 'goal', 'prompt']
        for field in required_fields:
            if not agent_config.get(field):
                errors.append(f"Le champ '{field}' est requis pour la configuration de l'agent")
        
        return errors
