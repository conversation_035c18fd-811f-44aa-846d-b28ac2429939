"""
Configuration centrale pour le système d'agents
"""

import os
from typing import Dict, Any

class Config:
    """Configuration principale du système d'agents"""
    
    # Configuration ChatGPT 4.1
    OPENAI_API_KEY = "sk-proj-pUkzo4FarIw0n_-w1T0Uq6DR32i0sr--LQnkNG8QDL0ZI97wdYP7p6Ca8tq6f1MLW5QXK7bWx9T3BLbkFJ-xhLBLQP8egicmVRslfJ1Ghw21eAKTBGBherW0V-6AZiBGyQSS0HDoNJ-j93TmE8d_FHNrYA"
    OPENAI_MODEL = "gpt-4-turbo-preview"  # ChatGPT 4.1
    OPENAI_MAX_TOKENS = 4000
    OPENAI_TEMPERATURE = 0.1
    
    # Configuration des agents
    AGENTS_CONFIG = {
        "error_analyzer": {
            "name": "Agent d'Analyse d'Erreurs",
            "role": "Analyste de Code et Détecteur d'Erreurs",
            "goal": "Analyser les fichiers de programme et d'erreur pour détecter, localiser et proposer des solutions aux erreurs",
            "prompt": """
            Tu es un expert en analyse de code et détection d'erreurs spécialisé dans les systèmes CAOFORS et le SQL embarqué.

            IMPORTANT: Tu dois TOUJOURS formater tes réponses selon ce modèle structuré avec emojis :

            🔍 Analyse de l'erreur :
            • [Point 1 avec emoji] : Explication détaillée
            • [Point 2 avec emoji] : Explication détaillée
            • [Point 3 avec emoji] : Explication détaillée

            ✅ Causes possibles :
            • [Cause 1] : Description précise
            • [Cause 2] : Description précise
            • [Cause 3] : Description précise
            • [Cause 4] : Description précise

            🛠️ Solutions possibles :
            • [Solution 1] : Action concrète à effectuer
            • [Solution 2] : Action concrète à effectuer
            • [Solution 3] : Action concrète à effectuer
            • [Solution 4] : Action concrète à effectuer

            💡 Recommandations préventives :
            • [Recommandation 1] : Bonne pratique à adopter
            • [Recommandation 2] : Bonne pratique à adopter

            Utilise des emojis appropriés (🔒 pour les verrous, ⚠️ pour les avertissements, 🔄 pour les processus, 📊 pour les données, etc.)
            Sois précis, technique et fournis des explications détaillées pour chaque point.
            """,
            "max_iterations": 5,
            "timeout": 300
        }
    }
    
    # Configuration des chemins
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    AGENTS_DIR = os.path.join(BASE_DIR, "agents")
    UTILS_DIR = os.path.join(BASE_DIR, "utils")
    TOOLS_DIR = os.path.join(BASE_DIR, "tools")
    EXAMPLES_DIR = os.path.join(BASE_DIR, "examples")
    
    # Configuration des logs
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE = os.path.join(BASE_DIR, "logs", "agents_system.log")
    
    # Configuration des types de fichiers supportés
    SUPPORTED_PROGRAM_EXTENSIONS = [".c", ".cpp", ".h", ".hpp", ".ec", ".txt"]
    SUPPORTED_ERROR_EXTENSIONS = [".error", ".err", ".log", ".txt"]
    
    @classmethod
    def get_agent_config(cls, agent_name: str) -> Dict[str, Any]:
        """Récupère la configuration d'un agent spécifique"""
        return cls.AGENTS_CONFIG.get(agent_name, {})
    
    @classmethod
    def validate_config(cls) -> bool:
        """Valide la configuration"""
        if not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is required")
        
        if not os.path.exists(cls.BASE_DIR):
            raise ValueError(f"Base directory does not exist: {cls.BASE_DIR}")
        
        return True

# Configuration par défaut
DEFAULT_CONFIG = Config()
