"""
Point d'entrée principal du système d'agents
"""

import sys
import os
import logging
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent))

from config.config import DEFAULT_CONFIG
from agents.error_analyzer_agent import <PERSON>rrorAnalyzerAgent
from utils.logger import setup_logger
from utils.file_handler import FileHandler

def setup_logging():
    """Configure le système de logging"""
    os.makedirs(os.path.dirname(DEFAULT_CONFIG.LOG_FILE), exist_ok=True)
    setup_logger(
        name="agents_system",
        log_file=DEFAULT_CONFIG.LOG_FILE,
        level=DEFAULT_CONFIG.LOG_LEVEL,
        format_str=DEFAULT_CONFIG.LOG_FORMAT
    )

def main():
    """Fonction principale"""
    try:
        # Configuration du logging
        setup_logging()
        logger = logging.getLogger("agents_system")
        
        logger.info("Démarrage du système d'agents")
        
        # Validation de la configuration
        DEFAULT_CONFIG.validate_config()
        logger.info("Configuration validée avec succès")
        
        # Exemple d'utilisation avec les fichiers d'exemple
        program_file = os.path.join(DEFAULT_CONFIG.EXAMPLES_DIR, "caofors program(1).txt")
        error_file = os.path.join(DEFAULT_CONFIG.EXAMPLES_DIR, "caofors_ORDER.11847.VSR1(3).error")
        
        if os.path.exists(program_file) and os.path.exists(error_file):
            logger.info(f"Analyse des fichiers d'exemple:")
            logger.info(f"  Programme: {program_file}")
            logger.info(f"  Erreurs: {error_file}")
            
            # Créer et utiliser l'agent d'analyse d'erreurs
            error_analyzer = ErrorAnalyzerAgent()
            
            # Analyser les fichiers
            result = error_analyzer.analyze_files(program_file, error_file)
            
            # Afficher les résultats
            print("\n" + "="*80)
            print("RÉSULTATS DE L'ANALYSE D'ERREURS")
            print("="*80)
            print(result)
            print("="*80)
            
            logger.info("Analyse terminée avec succès")
        else:
            logger.warning("Fichiers d'exemple non trouvés")
            print("Fichiers d'exemple non trouvés. Veuillez placer vos fichiers dans le dossier examples/")
            
    except Exception as e:
        logger.error(f"Erreur lors de l'exécution: {str(e)}")
        print(f"Erreur: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
