"""
Outil d'analyse de code pour détecter les erreurs et problèmes
"""

import re
import ast
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class ErrorSeverity(Enum):
    """Niveaux de sévérité des erreurs"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

@dataclass
class CodeError:
    """Représente une erreur dans le code"""
    line_number: int
    column: Optional[int]
    error_type: str
    severity: ErrorSeverity
    message: str
    suggestion: str
    code_snippet: str

class CodeAnalyzer:
    """Analyseur de code pour détecter les erreurs et problèmes"""
    
    def __init__(self):
        self.c_patterns = self._init_c_patterns()
        self.sql_patterns = self._init_sql_patterns()
        self.general_patterns = self._init_general_patterns()
    
    def _init_c_patterns(self) -> List[Dict[str, Any]]:
        """Initialise les patterns pour l'analyse du code C/C++"""
        return [
            {
                'pattern': r'#include\s*<([^>]+)>',
                'type': 'missing_include',
                'severity': ErrorSeverity.MEDIUM,
                'message': 'Include potentiellement manquant',
                'suggestion': 'Vérifiez que tous les includes nécessaires sont présents'
            },
            {
                'pattern': r'(\w+)\s*\(\s*\)\s*;',
                'type': 'function_declaration',
                'severity': ErrorSeverity.LOW,
                'message': 'Déclaration de fonction sans paramètres',
                'suggestion': 'Utilisez (void) pour les fonctions sans paramètres en C'
            },
            {
                'pattern': r'malloc\s*\([^)]+\)',
                'type': 'memory_allocation',
                'severity': ErrorSeverity.MEDIUM,
                'message': 'Allocation mémoire détectée',
                'suggestion': 'Vérifiez que la mémoire est libérée avec free()'
            },
            {
                'pattern': r'strcpy\s*\(',
                'type': 'unsafe_function',
                'severity': ErrorSeverity.HIGH,
                'message': 'Fonction strcpy non sécurisée',
                'suggestion': 'Utilisez strncpy() ou strcpy_s() à la place'
            },
            {
                'pattern': r'gets\s*\(',
                'type': 'unsafe_function',
                'severity': ErrorSeverity.CRITICAL,
                'message': 'Fonction gets() dangereuse',
                'suggestion': 'Utilisez fgets() à la place'
            }
        ]
    
    def _init_sql_patterns(self) -> List[Dict[str, Any]]:
        """Initialise les patterns pour l'analyse du SQL embarqué"""
        return [
            {
                'pattern': r'EXEC\s+SQL\s+([^;]+);',
                'type': 'embedded_sql',
                'severity': ErrorSeverity.LOW,
                'message': 'Requête SQL embarquée',
                'suggestion': 'Vérifiez la gestion des erreurs SQL'
            },
            {
                'pattern': r'SQLCODE\s*<\s*0',
                'type': 'sql_error_check',
                'severity': ErrorSeverity.LOW,
                'message': 'Vérification d\'erreur SQL',
                'suggestion': 'Bonne pratique de vérification des erreurs SQL'
            },
            {
                'pattern': r'SELECT\s+\*\s+FROM',
                'type': 'select_all',
                'severity': ErrorSeverity.MEDIUM,
                'message': 'SELECT * détecté',
                'suggestion': 'Spécifiez les colonnes explicitement pour de meilleures performances'
            }
        ]
    
    def _init_general_patterns(self) -> List[Dict[str, Any]]:
        """Initialise les patterns généraux"""
        return [
            {
                'pattern': r'TODO|FIXME|HACK|XXX',
                'type': 'todo_comment',
                'severity': ErrorSeverity.LOW,
                'message': 'Commentaire TODO/FIXME détecté',
                'suggestion': 'Résolvez les éléments en attente'
            },
            {
                'pattern': r'printf\s*\([^)]*%[sd][^)]*\)',
                'type': 'format_string',
                'severity': ErrorSeverity.MEDIUM,
                'message': 'Chaîne de format printf détectée',
                'suggestion': 'Vérifiez que les arguments correspondent au format'
            },
            {
                'pattern': r'if\s*\([^)]*=\s*[^=][^)]*\)',
                'type': 'assignment_in_condition',
                'severity': ErrorSeverity.HIGH,
                'message': 'Assignation possible dans une condition',
                'suggestion': 'Utilisez == pour la comparaison, = pour l\'assignation'
            }
        ]
    
    def analyze_code(self, content: str, file_type: str = "c") -> List[CodeError]:
        """
        Analyse le code et retourne les erreurs détectées
        
        Args:
            content: Contenu du fichier à analyser
            file_type: Type de fichier (c, cpp, sql, etc.)
        
        Returns:
            Liste des erreurs détectées
        """
        errors = []
        lines = content.splitlines()
        
        # Sélectionner les patterns appropriés
        patterns = self.general_patterns.copy()
        if file_type.lower() in ['c', 'cpp', 'h', 'hpp', 'ec']:
            patterns.extend(self.c_patterns)
            patterns.extend(self.sql_patterns)  # Pour le SQL embarqué
        
        # Analyser chaque ligne
        for line_num, line in enumerate(lines, 1):
            for pattern_info in patterns:
                matches = re.finditer(pattern_info['pattern'], line, re.IGNORECASE)
                for match in matches:
                    error = CodeError(
                        line_number=line_num,
                        column=match.start(),
                        error_type=pattern_info['type'],
                        severity=pattern_info['severity'],
                        message=pattern_info['message'],
                        suggestion=pattern_info['suggestion'],
                        code_snippet=line.strip()
                    )
                    errors.append(error)
        
        return errors
    
    def analyze_structure(self, content: str) -> Dict[str, Any]:
        """
        Analyse la structure du code
        
        Args:
            content: Contenu du fichier
        
        Returns:
            Informations sur la structure
        """
        lines = content.splitlines()
        
        structure = {
            'total_lines': len(lines),
            'code_lines': 0,
            'comment_lines': 0,
            'blank_lines': 0,
            'functions': [],
            'includes': [],
            'defines': [],
            'structs': []
        }
        
        for line in lines:
            stripped = line.strip()
            
            if not stripped:
                structure['blank_lines'] += 1
            elif stripped.startswith('//') or stripped.startswith('/*') or stripped.startswith('*'):
                structure['comment_lines'] += 1
            else:
                structure['code_lines'] += 1
                
                # Détecter les fonctions
                func_match = re.search(r'(\w+)\s+(\w+)\s*\([^)]*\)\s*{?', stripped)
                if func_match and not stripped.startswith('#'):
                    structure['functions'].append(func_match.group(2))
                
                # Détecter les includes
                include_match = re.search(r'#include\s*[<"]([^>"]+)[>"]', stripped)
                if include_match:
                    structure['includes'].append(include_match.group(1))
                
                # Détecter les defines
                define_match = re.search(r'#define\s+(\w+)', stripped)
                if define_match:
                    structure['defines'].append(define_match.group(1))
                
                # Détecter les structures
                struct_match = re.search(r'struct\s+(\w+)', stripped)
                if struct_match:
                    structure['structs'].append(struct_match.group(1))
        
        return structure
