#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utilitaire pour corriger la clé API en remplaçant les caractères cyrilliques
"""

def fix_api_key(api_key):
    """
    Remplace les caractères cyrilliques par leurs équivalents ASCII
    """
    # Mapping des caractères cyrilliques vers ASCII
    cyrillic_to_ascii = {
        'г': 'r',  # г -> r
        'о': 'o',  # о -> o  
        'і': 'i',  # і -> i
        'З': '3',  # З -> 3
        'ß': 'B',  # ß -> B
        '—': '-',  # — -> -
    }
    
    fixed_key = api_key
    
    print("🔧 Correction de la clé API...")
    print(f"Clé originale: {api_key}")
    
    for cyrillic, ascii_char in cyrillic_to_ascii.items():
        if cyrillic in fixed_key:
            print(f"  Remplacement: '{cyrillic}' -> '{ascii_char}'")
            fixed_key = fixed_key.replace(cyrillic, ascii_char)
    
    print(f"Clé corrigée: {fixed_key}")
    
    return fixed_key

def test_fixed_key():
    """Test avec la clé corrigée"""
    original_key = "sk-pгoj-pUkzо4FarIw0n_-w1T0Uq6DR32і0sг--LQnkNG8QDL0ZI97wdYP7p6Ca8tq6f1MLW5QXK7bWx9TЗBLbkFJ—xhLßLQP8egicmVRslfJ1Ghw21eAKTBGBherW0V-6AZіBGyQSS0HDoNJ-j9ЗTmE8d_FHNгYA"
    
    fixed_key = fix_api_key(original_key)
    
    print(f"\n📝 Clé API corrigée:")
    print(f"{fixed_key}")
    
    # Vérifier que la clé ne contient que des caractères ASCII
    try:
        fixed_key.encode('ascii')
        print("✅ La clé corrigée contient uniquement des caractères ASCII")
        return fixed_key
    except UnicodeEncodeError as e:
        print(f"❌ La clé contient encore des caractères non-ASCII: {e}")
        return None

if __name__ == "__main__":
    fixed_key = test_fixed_key()
    if fixed_key:
        print(f"\n🎯 Utilisez cette clé corrigée:")
        print(f"{fixed_key}")
    else:
        print("\n💥 Impossible de corriger la clé API")
